[0;31m[ERROR][0m Unknown option: 2
Usage: ./scripts/build.sh COMMAND [OPTIONS]

Unified Build Script for OMOP ETL Pipeline

COMMANDS:
    build               Build Docker images for specified target (shows build summary)
    test               Run tests for specified target (shows test summary)
    dev                Start development environment (shows summary when done)
    clean              Clean build artifacts and containers
    lint               Run static analysis (clang-tidy, cppcheck)
    format             Format code using clang-format
    docs               Build documentation (shows build summary)
    up                 Start services with docker-compose
    down               Stop and remove services
    shell              Start interactive shell in development container
    detect-arch        Detect system architecture and provide recommendations
    config             Generate configuration from templates

BUILD OPTIONS:
    -t, --target TARGET     Build target: api cli common core cdm extract transform load service security ml all (default: api)
    -b, --build-type TYPE   Build type: release debug (default: release)
    -p, --platform PLATFORM Docker platform (auto-detected if not specified)
    --tests                 Enable test building and execution
    --dev-tools            Enable development tools in container
    --grpc                 Enable gRPC support
    --rest-api             Enable REST API support
    -c, --clean            Clean build (remove existing containers and images)

DOCKER OPTIONS:
    --push                 Push image to registry after build
    -r, --registry REGISTRY Registry URL (e.g., docker.io/myorg)
    --tag TAG              Image tag (default: latest)
    --profiles PROFILES    Docker compose profiles (default: default)

TEST OPTIONS:
    --filter FILTER        Run only tests matching filter
    --test-type TYPE       Test type: unit, integration (default: unit)
    -v, --verbose          Enable verbose test output

ENVIRONMENT OPTIONS:
    -e, --env ENVIRONMENT  Target environment: dev, staging, prod
    --config-file FILE     Use specific configuration file
    --no-auto-render       Skip automatic configuration rendering

EXAMPLES:
    ./scripts/build.sh build                               # Build API service (default)
    ./scripts/build.sh build -t cli --tests               # Build CLI with tests
    ./scripts/build.sh build -t all -b debug --dev-tools  # Build all targets in debug with dev tools
    ./scripts/build.sh test -t extract --test-type unit   # Run unit tests for extract target
    ./scripts/build.sh test -t extract --test-type integration  # Run integration tests for extract target
    ./scripts/build.sh test -t extract --filter "ConnectionPool*"  # Run extract tests with filter
    ./scripts/build.sh dev                                 # Start development environment
    ./scripts/build.sh up --profiles "api,postgres"       # Start API and database services
    ./scripts/build.sh clean                               # Clean all build artifacts
    ./scripts/build.sh docs                                # Build documentation
    ./scripts/build.sh detect-arch                         # Detect architecture
    
ENVIRONMENT EXAMPLES:
    ./scripts/build.sh config --env dev                    # Generate dev configuration
    ./scripts/build.sh build -e prod                       # Build for production environment
    ./scripts/build.sh dev -e staging                      # Start staging development environment
    ./scripts/build.sh config --env prod --validate       # Validate production config

TARGET DESCRIPTIONS:
    api        - REST API service with gRPC support
    cli        - Command-line interface application
    common     - Common utilities and configuration libraries
    core       - Core pipeline and job management
    cdm        - OMOP CDM table definitions and schemas
    extract    - Data extraction from various sources
    transform  - Data transformation and validation
    load       - Data loading and batch processing
    service    - Service layer and microservice components
    security   - Security and authentication components
    ml         - Machine learning and medical term classification
    all        - Build all targets

SUMMARY FEATURES:
    The script now provides comprehensive summaries after operations:
    
    BUILD SUMMARY:
    - Lists all successfully built targets with Docker run commands
    - Shows generated Docker images with sizes, IDs, and creation dates
    - Displays build duration and configuration details
    - Reports any build errors or issues with recommendations
    - Shows created artifacts with relative paths and file sizes
    - Provides specific Docker commands to run each target
    - Includes file type indicators (📄 files, 📁 directories, 🐳 images)
    
    TEST SUMMARY:
    - Reports test execution results (passed/failed) with statistics
    - Shows test duration and configuration details
    - Displays test report files with viewing commands
    - Lists coverage reports (HTML, XML, LCOV) with access instructions
    - Provides specific commands to view different report types
    - Includes relative paths for all test artifacts
    - Shows test log locations and failure details
    
    ARTIFACT TRACKING:
    - Discovers actual build outputs: compiled libraries (.so, .a, .dylib)
    - Finds executables and binaries (excluding test executables)
    - Locates generated documentation (HTML, Doxygen output)
    - Tracks CMake-generated files (CMakeCache.txt, compile_commands.json)
    - Finds configuration files and pkg-config files
    - Shows relative paths from project root with file sizes
    - Automatically discovers test reports (JUnit XML, HTML, JSON, logs)
    - Finds coverage reports in common locations
    
    DOCKER INTEGRATION:
    - Shows specific run commands for each image type (API, CLI, service)
    - Includes port mappings and volume mounts
    - Provides access URLs for web services
    - Shows development environment setup commands

