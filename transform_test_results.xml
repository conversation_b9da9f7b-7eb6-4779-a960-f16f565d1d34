<?xml version="1.0" encoding="UTF-8"?>
<testsuites tests="377" failures="7" disabled="0" errors="0" time="6.974" timestamp="2025-08-02T17:20:30.469" name="AllTests">
  <testsuite name="AllTransformationsTest" tests="17" failures="0" disabled="0" skipped="0" errors="0" time="0.008" timestamp="2025-08-02T17:20:30.469">
    <testcase name="AdvancedConditionalTransformationComplexLogic" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="93" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:30.469" classname="AllTransformationsTest" />
    <testcase name="BatchProcessingMixedTransformationTypes" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="134" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.470" classname="AllTransformationsTest" />
    <testcase name="CompositeTransformationErrorRecovery" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="160" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.471" classname="AllTransformationsTest" />
    <testcase name="CustomJavaScriptTransformationComplexExpressions" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="190" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.471" classname="AllTransformationsTest" />
    <testcase name="DateTransformationTimezoneHandling" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="212" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.471" classname="AllTransformationsTest" />
    <testcase name="FieldMappingTransformationComplexMappings" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="233" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.472" classname="AllTransformationsTest" />
    <testcase name="NumericTransformationPrecisionHandling" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="276" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.473" classname="AllTransformationsTest" />
    <testcase name="PluginTransformationSecurityValidation" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="296" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.473" classname="AllTransformationsTest" />
    <testcase name="PythonTransformationVariableBinding" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="315" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.473" classname="AllTransformationsTest" />
    <testcase name="SQLTransformationConditionalLogic" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="339" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:30.473" classname="AllTransformationsTest" />
    <testcase name="StringConcatenationCustomSeparators" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="372" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.476" classname="AllTransformationsTest" />
    <testcase name="StringTransformationPatternMatching" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="396" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.476" classname="AllTransformationsTest" />
    <testcase name="TransformationRegistryFunctionality" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="414" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.476" classname="AllTransformationsTest" />
    <testcase name="ValidationEngineComprehensiveFieldValidation" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="431" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.476" classname="AllTransformationsTest" />
    <testcase name="VocabularyTransformationHierarchyNavigation" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="461" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.477" classname="AllTransformationsTest" />
    <testcase name="VocabularyTransformationMultipleMappings" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="481" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.477" classname="AllTransformationsTest" />
    <testcase name="ZeroValueHandlingAllTransformationTypes" file="/workspace/tests/unit/transform/all_transformations_comprehensive_test.cpp" line="499" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.477" classname="AllTransformationsTest" />
  </testsuite>
  <testsuite name="ComprehensiveEdgeCasesTest" tests="18" failures="0" disabled="0" skipped="0" errors="0" time="0.22" timestamp="2025-08-02T17:20:30.477">
    <testcase name="BoundaryConditionsEmptyInputValues" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="90" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.477" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="BoundaryConditionsExtremelyLargeNumbers" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="106" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.477" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="BoundaryConditionsInvalidDateFormats" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="125" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.477" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="BoundaryConditionsNullUndefinedValues" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="150" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.478" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="BoundaryConditionsVeryLongStrings" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="166" status="run" result="completed" time="0.087" timestamp="2025-08-02T17:20:30.478" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="ComplexNestedTransformationErrorHandling" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="185" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.565" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="ConcurrentTransformationSafety" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="223" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:30.566" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="ErrorPropagationThroughTransformationChains" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="258" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.567" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="JavaScriptTransformationSyntaxErrors" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="272" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.567" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="MemoryExhaustionLargeBatchProcessing" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="290" status="run" result="completed" time="0.129" timestamp="2025-08-02T17:20:30.567" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="PluginTransformationInvalidLibraryPaths" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="318" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.696" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="PythonTransformationScriptCompilationErrors" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="332" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.696" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="SQLTransformationMalformedQueries" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="348" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.696" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="StringTransformationInvalidRegularExpressions" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="364" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.697" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="TypeConversionEdgeCases" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="379" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.697" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="ValidationEngineComplexRules" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="411" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.697" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="VocabularyTransformationCircularReferences" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="443" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.697" classname="ComprehensiveEdgeCasesTest" />
    <testcase name="ZeroLengthMaximumLengthInputHandling" file="/workspace/tests/unit/transform/comprehensive_edge_cases_test.cpp" line="462" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.698" classname="ComprehensiveEdgeCasesTest" />
  </testsuite>
  <testsuite name="ConditionalTransformationsTest" tests="13" failures="0" disabled="0" skipped="0" errors="0" time="0.001" timestamp="2025-08-02T17:20:30.698">
    <testcase name="AdvancedConditionalSimpleCondition" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="28" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.698" classname="ConditionalTransformationsTest" />
    <testcase name="AdvancedConditionalNumericConditions" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="68" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.698" classname="ConditionalTransformationsTest" />
    <testcase name="AdvancedConditionalNestedConditions" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="107" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.698" classname="ConditionalTransformationsTest" />
    <testcase name="AdvancedConditionalStringOperations" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="155" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.698" classname="ConditionalTransformationsTest" />
    <testcase name="AdvancedConditionalNullChecks" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="207" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.698" classname="ConditionalTransformationsTest" />
    <testcase name="AdvancedConditionalTransformAction" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="239" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.699" classname="ConditionalTransformationsTest" />
    <testcase name="LookupTableCaseInsensitive" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="262" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.699" classname="ConditionalTransformationsTest" />
    <testcase name="LookupTablePassThrough" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="278" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.699" classname="ConditionalTransformationsTest" />
    <testcase name="LookupTableNumericKeys" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="293" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.699" classname="ConditionalTransformationsTest" />
    <testcase name="AdvancedConditionalMultipleRules" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="310" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.699" classname="ConditionalTransformationsTest" />
    <testcase name="AdvancedConditionalNegatedCondition" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="361" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.699" classname="ConditionalTransformationsTest" />
    <testcase name="AdvancedConditionalBetweenOperator" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="389" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.699" classname="ConditionalTransformationsTest" />
    <testcase name="AdvancedConditionalInOperator" file="/workspace/tests/unit/transform/conditional_transformations_test.cpp" line="416" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.699" classname="ConditionalTransformationsTest" />
  </testsuite>
  <testsuite name="CustomTransformationsTest" tests="20" failures="0" disabled="0" skipped="0" errors="0" time="0.004" timestamp="2025-08-02T17:20:30.699">
    <testcase name="JavaScriptTransformationSimpleExpression" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="27" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.699" classname="CustomTransformationsTest" />
    <testcase name="JavaScriptTransformationLength" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="44" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.699" classname="CustomTransformationsTest" />
    <testcase name="JavaScriptTransformationMultiplication" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="60" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.699" classname="CustomTransformationsTest" />
    <testcase name="JavaScriptTransformationBoolean" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="76" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.700" classname="CustomTransformationsTest" />
    <testcase name="JavaScriptTransformationWithImports" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="92" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.700" classname="CustomTransformationsTest" />
    <testcase name="SQLTransformationUppercase" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="109" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.701" classname="CustomTransformationsTest" />
    <testcase name="SQLTransformationLength" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="125" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.701" classname="CustomTransformationsTest" />
    <testcase name="SQLTransformationWithContext" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="141" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.701" classname="CustomTransformationsTest" />
    <testcase name="PluginTransformationConfiguration" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="164" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.702" classname="CustomTransformationsTest" />
    <testcase name="PythonTransformationUppercase" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="189" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.702" classname="CustomTransformationsTest" />
    <testcase name="PythonTransformationLength" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="204" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.702" classname="CustomTransformationsTest" />
    <testcase name="PythonTransformationWithModules" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="219" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.703" classname="CustomTransformationsTest" />
    <testcase name="CompositeTransformationSingleStep" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="237" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.703" classname="CustomTransformationsTest" />
    <testcase name="CompositeTransformationMultipleSteps" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="258" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.703" classname="CustomTransformationsTest" />
    <testcase name="CompositeTransformationStopOnError" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="298" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="CustomTransformationsTest" />
    <testcase name="CompositeTransformationMetadata" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="333" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="CustomTransformationsTest" />
    <testcase name="JavaScriptTransformationInvalidExpression" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="359" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="CustomTransformationsTest" />
    <testcase name="SQLTransformationNumericResult" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="376" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="CustomTransformationsTest" />
    <testcase name="PythonTransformationScriptFile" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="393" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="CustomTransformationsTest" />
    <testcase name="CompositeTransformationMixedTypes" file="/workspace/tests/unit/transform/custom_transformations_test.cpp" line="409" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="CustomTransformationsTest" />
  </testsuite>
  <testsuite name="DateTransformationsTest" tests="19" failures="0" disabled="0" skipped="0" errors="0" time="0." timestamp="2025-08-02T17:20:30.704">
    <testcase name="DateTransformationStringInput" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="39" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateTransformationTimePointInput" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="57" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateTransformationInvalidInput" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="73" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateTransformationValidation" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="86" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateTransformationType" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="96" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateCalculationAge" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="102" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateCalculationAgeBeforeBirthday" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="120" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateCalculationDateDiffDays" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="138" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateCalculationDateDiffMonths" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="156" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateCalculationAddDays" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="174" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateCalculationAddMonths" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="190" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateCalculationAddYears" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="206" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateCalculationStartOfMonth" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="222" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateCalculationEndOfMonth" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="237" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateCalculationStartOfYear" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="252" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.704" classname="DateTransformationsTest" />
    <testcase name="DateCalculationEndOfYear" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="267" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="DateTransformationsTest" />
    <testcase name="DateCalculationTimePointInput" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="282" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="DateTransformationsTest" />
    <testcase name="DateRangeValidationTimePointInput" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="300" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="DateTransformationsTest" />
    <testcase name="DateCalculationReferenceNow" file="/workspace/tests/unit/transform/date_transformations_test.cpp" line="318" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="DateTransformationsTest" />
  </testsuite>
  <testsuite name="FieldTransformationHelpersTest" tests="26" failures="0" disabled="0" skipped="0" errors="0" time="0.006" timestamp="2025-08-02T17:20:30.705">
    <testcase name="FieldMappingBasic" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="31" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationChainSingle" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="49" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationChainMultiple" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="66" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationChainClear" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="94" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationChainError" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="109" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="FieldTransformationBuilder" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="128" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="FieldTransformationBuilderWithParams" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="150" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="BatchFieldTransformerBasic" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="171" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="BatchFieldTransformerMultipleMappings" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="193" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="BatchFieldTransformerDefaultValue" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="223" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="BatchFieldTransformerCopyUnmapped" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="246" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="BatchFieldTransformerClearMappings" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="274" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationCacheBasic" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="289" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationCacheLRU" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="310" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationCacheStats" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="334" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationCacheClear" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="357" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationMetricsRecord" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="375" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationMetricsByType" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="405" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationMetricsFieldNames" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="427" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationMetricsReset" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="444" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationMetricsAverageDuration" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="460" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationCacheThreadSafety" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="473" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:30.705" classname="FieldTransformationHelpersTest" />
    <testcase name="TransformationMetricsThreadSafety" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="507" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:30.708" classname="FieldTransformationHelpersTest" />
    <testcase name="ComplexTransformationScenario" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="540" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.710" classname="FieldTransformationHelpersTest" />
    <testcase name="CacheKeyComparison" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="604" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationHelpersTest" />
    <testcase name="CacheOperations" file="/workspace/tests/unit/transform/field_transformation_helpers_test.cpp" line="613" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationHelpersTest" />
  </testsuite>
  <testsuite name="FieldTransformationTest" tests="19" failures="0" disabled="0" skipped="0" errors="0" time="0.001" timestamp="2025-08-02T17:20:30.711">
    <testcase name="DirectTransformationPassThrough" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="26" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="DirectTransformationValidation" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="37" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="DirectTransformationType" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="46" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="DateTransformationStringInput" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="52" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="DateTransformationTimePointInput" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="71" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="DateTransformationInvalidInput" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="87" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="DateTransformationValidation" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="102" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="NumericTransformationMultiply" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="112" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="NumericTransformationDivide" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="128" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="NumericTransformationDivideByZero" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="144" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="NumericTransformationRound" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="160" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="NumericTransformationConstraints" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="176" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="NumericTransformationStringInput" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="200" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="StringConcatenationSingleField" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="216" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="StringConcatenationMultipleFields" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="232" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="ConditionalTransformationStringEquals" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="255" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.711" classname="FieldTransformationTest" />
    <testcase name="ConditionalTransformationNumericComparison" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="282" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="FieldTransformationTest" />
    <testcase name="ConditionalTransformationNullChecks" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="308" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="FieldTransformationTest" />
    <testcase name="ConditionalTransformationRegexMatch" file="/workspace/tests/unit/transform/field_transformation_test.cpp" line="333" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="FieldTransformationTest" />
  </testsuite>
  <testsuite name="NumericTransformationsTest" tests="34" failures="0" disabled="0" skipped="0" errors="0" time="0." timestamp="2025-08-02T17:20:30.712">
    <testcase name="NumericTransformationMultiply" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="31" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="NumericTransformationsTest" />
    <testcase name="NumericTransformationDivide" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="47" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="NumericTransformationsTest" />
    <testcase name="NumericTransformationDivideByZero" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="63" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="NumericTransformationsTest" />
    <testcase name="NumericTransformationRound" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="77" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="NumericTransformationsTest" />
    <testcase name="NumericTransformationConstraints" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="93" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="NumericTransformationsTest" />
    <testcase name="NumericTransformationStringInput" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="117" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="NumericTransformationsTest" />
    <testcase name="NumericTransformationInvalidStringInput" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="133" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="NumericTransformationsTest" />
    <testcase name="NumericTransformationValidation" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="147" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="NumericTransformationsTest" />
    <testcase name="NumericTransformationType" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="158" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericUnitConversion" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="164" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericLogarithm" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="181" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.712" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericExponential" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="197" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericPower" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="213" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericSquareRoot" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="229" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericPercentage" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="244" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericZScore" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="260" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericMinMax" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="277" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericClamp" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="294" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericBucketRange" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="311" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericWithRounding" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="328" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericOutputAsInteger" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="345" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="NumericValidationValidRange" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="362" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="NumericValidationPrecisionEnforcement" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="378" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="NumericValidationStringInput" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="394" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="NumericValidationOutOfRange" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="410" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="NumericValidationNaN" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="424" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="NumericValidationInfinity" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="437" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="NumericValidationAllowedValues" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="450" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="NumericValidationWithClamping" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="463" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="NumericValidationClampToMax" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="479" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericUnitConversionMissingUnits" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="495" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericLogarithmNegative" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="508" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericSquareRootNegative" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="521" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
    <testcase name="AdvancedNumericInvalidInput" file="/workspace/tests/unit/transform/numeric_transformations_test.cpp" line="534" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="NumericTransformationsTest" />
  </testsuite>
  <testsuite name="StringTransformationsTest" tests="19" failures="0" disabled="0" skipped="0" errors="0" time="0.001" timestamp="2025-08-02T17:20:30.713">
    <testcase name="StringManipulationUppercase" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="29" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="StringTransformationsTest" />
    <testcase name="StringManipulationLowercase" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="44" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="StringTransformationsTest" />
    <testcase name="StringManipulationTitleCase" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="59" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="StringTransformationsTest" />
    <testcase name="StringManipulationTrim" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="74" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="StringTransformationsTest" />
    <testcase name="StringManipulationPadLeft" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="89" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="StringTransformationsTest" />
    <testcase name="StringManipulationSubstring" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="106" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="StringTransformationsTest" />
    <testcase name="StringManipulationReplace" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="123" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="StringTransformationsTest" />
    <testcase name="StringManipulationRemoveNonAlphanumeric" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="140" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="StringTransformationsTest" />
    <testcase name="StringManipulationNormalizeWhitespace" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="156" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="StringTransformationsTest" />
    <testcase name="StringManipulationMaxLength" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="171" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="StringTransformationsTest" />
    <testcase name="StringPatternExtractionEmail" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="188" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.713" classname="StringTransformationsTest" />
    <testcase name="StringPatternExtractionPhone" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="203" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.714" classname="StringTransformationsTest" />
    <testcase name="StringPatternExtractionSSN" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="220" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.714" classname="StringTransformationsTest" />
    <testcase name="StringPatternExtractionCustom" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="235" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.714" classname="StringTransformationsTest" />
    <testcase name="StringPatternExtractionCaptureGroup" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="251" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.714" classname="StringTransformationsTest" />
    <testcase name="StringPatternExtractionExtractAll" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="268" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.715" classname="StringTransformationsTest" />
    <testcase name="StringPatternExtractionNoMatchDefault" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="285" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.715" classname="StringTransformationsTest" />
    <testcase name="StringPatternExtractionInvalidInput" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="302" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.715" classname="StringTransformationsTest" />
    <testcase name="StringManipulationConstCharInput" file="/workspace/tests/unit/transform/string_transformations_test.cpp" line="315" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.715" classname="StringTransformationsTest" />
  </testsuite>
  <testsuite name="RegistryTest" tests="2" failures="0" disabled="0" skipped="0" errors="0" time="0." timestamp="2025-08-02T17:20:30.715">
    <testcase name="CheckRegisteredTransformations" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="312" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.715" classname="RegistryTest" />
    <testcase name="ManualRegisterAllTransformations" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="336" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.715" classname="RegistryTest" />
  </testsuite>
  <testsuite name="TransformIntegrationTest" tests="11" failures="0" disabled="0" skipped="0" errors="0" time="0.069" timestamp="2025-08-02T17:20:30.715">
    <testcase name="CompletePersonTransformation" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="408" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:30.715" classname="TransformIntegrationTest" />
    <testcase name="ErrorPropagationWithContext" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="437" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:30.717" classname="TransformIntegrationTest" />
    <testcase name="BatchTransformationMixedRecords" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="470" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:30.719" classname="TransformIntegrationTest" />
    <testcase name="TransformationChainErrorHandling" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="515" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:30.721" classname="TransformIntegrationTest" />
    <testcase name="ConditionalTransformationInEngine" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="540" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:30.724" classname="TransformIntegrationTest" />
    <testcase name="CustomTransformations" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="574" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:30.726" classname="TransformIntegrationTest" />
    <testcase name="ComplexTransformationChain" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="610" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:30.728" classname="TransformIntegrationTest" />
    <testcase name="AllTransformationTypes" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="675" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:30.731" classname="TransformIntegrationTest" />
    <testcase name="LargeBatchPerformance" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="712" status="run" result="completed" time="0.04" timestamp="2025-08-02T17:20:30.734" classname="TransformIntegrationTest" />
    <testcase name="TransformationMetadata" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="745" status="run" result="completed" time="0.003" timestamp="2025-08-02T17:20:30.774" classname="TransformIntegrationTest" />
    <testcase name="FullPipelineIntegration" file="/workspace/tests/unit/transform/transform_integration_test.cpp" line="772" status="run" result="completed" time="0.006" timestamp="2025-08-02T17:20:30.777" classname="TransformIntegrationTest" />
  </testsuite>
  <testsuite name="TransformationUtilsTest" tests="46" failures="4" disabled="0" skipped="0" errors="0" time="0.002" timestamp="2025-08-02T17:20:30.784">
    <testcase name="ParseDateWithValidFormats" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="23" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.784" classname="TransformationUtilsTest" />
    <testcase name="ParseDateWithInvalidFormat" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="37" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="ParseDateWithISO8601Fallback" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="45" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="FormatDateToString" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="53" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="ConvertLengthUnits" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="64" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="ConvertWeightUnits" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="76" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="ConvertTemperatureUnits" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="85" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="ConvertSameUnits" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="100" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="ConvertUnsupportedUnits" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="106" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="NormalizeStringCaseSensitive" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="114" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="NormalizeStringCaseInsensitive" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="120" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="NormalizeStringNoTrim" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="126" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="ValidateNumericRangeValid" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="132" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="ValidateNumericRangeInvalid" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="139" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="ValidateNumericRangeOptionalBounds" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="145" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="ExtractNumericFromString" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="151" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="MatchesPatternValid" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="159" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.785" classname="TransformationUtilsTest" />
    <testcase name="MatchesPatternInvalid" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="165" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="MatchesPatternInvalidRegex" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="170" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="SplitString" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="178" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="SplitStringEmptyParts" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="187" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="JoinStrings" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="196" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="JoinStringsEmpty" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="203" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="CalculateAge" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="210" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="CalculateAgeBeforeBirthday" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="229" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="CalculateDateDifferenceDays" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="247" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="CalculateDateDifferenceHours" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="256" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="CalculateDateDifferenceInvalidUnit" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="265" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="UKNHSNumberValidation" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="274" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest" />
    <testcase name="LeafConceptIdentification" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="294" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.786" classname="TransformationUtilsTest">
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:296&#x0A;Value of: TransformationUtils::is_leaf_concept(1001)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:296
Value of: TransformationUtils::is_leaf_concept(1001)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:297&#x0A;Value of: TransformationUtils::is_leaf_concept(2001)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:297
Value of: TransformationUtils::is_leaf_concept(2001)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:298&#x0A;Value of: TransformationUtils::is_leaf_concept(3001)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:298
Value of: TransformationUtils::is_leaf_concept(3001)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:299&#x0A;Value of: TransformationUtils::is_leaf_concept(4001)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:299
Value of: TransformationUtils::is_leaf_concept(4001)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:300&#x0A;Value of: TransformationUtils::is_leaf_concept(5001)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:300
Value of: TransformationUtils::is_leaf_concept(5001)
  Actual: false
Expected: true
]]></failure>
    </testcase>
    <testcase name="RootConceptIdentification" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="310" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest">
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:312&#x0A;Value of: TransformationUtils::is_root_concept(1000)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:312
Value of: TransformationUtils::is_root_concept(1000)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:313&#x0A;Value of: TransformationUtils::is_root_concept(2000)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:313
Value of: TransformationUtils::is_root_concept(2000)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:314&#x0A;Value of: TransformationUtils::is_root_concept(3000)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:314
Value of: TransformationUtils::is_root_concept(3000)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:315&#x0A;Value of: TransformationUtils::is_root_concept(4000)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:315
Value of: TransformationUtils::is_root_concept(4000)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:316&#x0A;Value of: TransformationUtils::is_root_concept(5000)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:316
Value of: TransformationUtils::is_root_concept(5000)
  Actual: false
Expected: true
]]></failure>
    </testcase>
    <testcase name="AncestorConceptRelationship" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="328" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest">
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:330&#x0A;Value of: TransformationUtils::is_ancestor_concept(1000, 1001)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:330
Value of: TransformationUtils::is_ancestor_concept(1000, 1001)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:331&#x0A;Value of: TransformationUtils::is_ancestor_concept(2000, 2002)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:331
Value of: TransformationUtils::is_ancestor_concept(2000, 2002)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:332&#x0A;Value of: TransformationUtils::is_ancestor_concept(3000, 3003)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:332
Value of: TransformationUtils::is_ancestor_concept(3000, 3003)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:333&#x0A;Value of: TransformationUtils::is_ancestor_concept(4000, 4004)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:333
Value of: TransformationUtils::is_ancestor_concept(4000, 4004)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:334&#x0A;Value of: TransformationUtils::is_ancestor_concept(5000, 5005)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:334
Value of: TransformationUtils::is_ancestor_concept(5000, 5005)
  Actual: false
Expected: true
]]></failure>
    </testcase>
    <testcase name="DescendantConceptRelationship" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="345" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest">
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:347&#x0A;Value of: TransformationUtils::is_descendant_concept(1001, 1000)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:347
Value of: TransformationUtils::is_descendant_concept(1001, 1000)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:348&#x0A;Value of: TransformationUtils::is_descendant_concept(2002, 2000)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:348
Value of: TransformationUtils::is_descendant_concept(2002, 2000)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:349&#x0A;Value of: TransformationUtils::is_descendant_concept(3003, 3000)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:349
Value of: TransformationUtils::is_descendant_concept(3003, 3000)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:350&#x0A;Value of: TransformationUtils::is_descendant_concept(4004, 4000)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:350
Value of: TransformationUtils::is_descendant_concept(4004, 4000)
  Actual: false
Expected: true
]]></failure>
      <failure message="/workspace/tests/unit/transform/transform_utils_test.cpp:351&#x0A;Value of: TransformationUtils::is_descendant_concept(5005, 5000)&#x0A;  Actual: false&#x0A;Expected: true&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transform_utils_test.cpp:351
Value of: TransformationUtils::is_descendant_concept(5005, 5000)
  Actual: false
Expected: true
]]></failure>
    </testcase>
    <testcase name="RequiredFieldValidation" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="364" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="OptionalFieldValidation" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="385" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="MandatoryFieldValidation" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="398" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="PrimaryFieldValidation" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="415" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="SecondaryFieldValidation" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="430" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="ActiveStatusIdentification" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="445" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="InactiveStatusIdentification" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="467" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="EnabledStatusIdentification" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="489" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="DisabledStatusIdentification" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="511" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="VisibleStatusIdentification" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="533" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="HiddenStatusIdentification" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="556" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="EditableStatusIdentification" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="579" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
    <testcase name="ReadonlyStatusIdentification" file="/workspace/tests/unit/transform/transform_utils_test.cpp" line="601" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationUtilsTest" />
  </testsuite>
  <testsuite name="TransformationEdgeCasesTest" tests="10" failures="0" disabled="0" skipped="0" errors="0" time="0.727" timestamp="2025-08-02T17:20:30.787">
    <testcase name="NumericTransformationEdgeCases" file="/workspace/tests/unit/transform/transformation_edge_cases_test.cpp" line="29" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.787" classname="TransformationEdgeCasesTest" />
    <testcase name="StringTransformationEdgeCases" file="/workspace/tests/unit/transform/transformation_edge_cases_test.cpp" line="62" status="run" result="completed" time="0.007" timestamp="2025-08-02T17:20:30.787" classname="TransformationEdgeCasesTest" />
    <testcase name="DateTransformationEdgeCases" file="/workspace/tests/unit/transform/transformation_edge_cases_test.cpp" line="98" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.794" classname="TransformationEdgeCasesTest" />
    <testcase name="ConditionalTransformationEdgeCases" file="/workspace/tests/unit/transform/transformation_edge_cases_test.cpp" line="138" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.795" classname="TransformationEdgeCasesTest" />
    <testcase name="MixedTypeInputs" file="/workspace/tests/unit/transform/transformation_edge_cases_test.cpp" line="179" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.795" classname="TransformationEdgeCasesTest" />
    <testcase name="PatternExtractionEdgeCases" file="/workspace/tests/unit/transform/transformation_edge_cases_test.cpp" line="207" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.795" classname="TransformationEdgeCasesTest" />
    <testcase name="VocabularyTransformationEdgeCases" file="/workspace/tests/unit/transform/transformation_edge_cases_test.cpp" line="242" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.795" classname="TransformationEdgeCasesTest" />
    <testcase name="TransformationChainEdgeCases" file="/workspace/tests/unit/transform/transformation_edge_cases_test.cpp" line="319" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:30.795" classname="TransformationEdgeCasesTest" />
    <testcase name="RecordBatchEdgeCases" file="/workspace/tests/unit/transform/transformation_edge_cases_test.cpp" line="363" status="run" result="completed" time="0.695" timestamp="2025-08-02T17:20:30.795" classname="TransformationEdgeCasesTest" />
    <testcase name="ProcessingContextEdgeCases" file="/workspace/tests/unit/transform/transformation_edge_cases_test.cpp" line="402" status="run" result="completed" time="0.024" timestamp="2025-08-02T17:20:31.491" classname="TransformationEdgeCasesTest" />
  </testsuite>
  <testsuite name="TransformationEngineTest" tests="18" failures="2" disabled="0" skipped="0" errors="0" time="0.032" timestamp="2025-08-02T17:20:31.515">
    <testcase name="InitializeEngine" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="301" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:31.515" classname="TransformationEngineTest" />
    <testcase name="InitializeEngineNoTableName" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="309" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.517" classname="TransformationEngineTest" />
    <testcase name="InitializeEngineInvalidTable" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="319" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:31.519" classname="TransformationEngineTest" />
    <testcase name="TransformRecord" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="330" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.521" classname="TransformationEngineTest" />
    <testcase name="TransformRecordFiltered" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="355" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.523" classname="TransformationEngineTest" />
    <testcase name="TransformBatch" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="374" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.524" classname="TransformationEngineTest" />
    <testcase name="TransformWithValidation" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="408" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:31.526" classname="TransformationEngineTest" />
    <testcase name="TransformWithStrictValidation" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="429" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.528" classname="TransformationEngineTest" />
    <testcase name="GetEngineType" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="449" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.530" classname="TransformationEngineTest" />
    <testcase name="ValidateRecord" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="454" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.531" classname="TransformationEngineTest" />
    <testcase name="GetStatistics" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="465" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.533" classname="TransformationEngineTest" />
    <testcase name="RegisterCustomTransformation" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="486" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:31.534" classname="TransformationEngineTest" />
    <testcase name="TransformationError" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="495" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.536" classname="TransformationEngineTest" />
    <testcase name="TransformationEngineFactory" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="518" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.538" classname="TransformationEngineTest" />
    <testcase name="MultipleFieldTransformations" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="528" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.539" classname="TransformationEngineTest">
      <failure message="/workspace/tests/unit/transform/transformation_engine_test.cpp:575&#x0A;Expected equality of these values:&#x0A;  std::any_cast&lt;std::string&gt;(result-&gt;getField(&quot;full_name&quot;))&#x0A;    Which is: &quot;John&quot;&#x0A;  &quot;John Smith&quot;&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transformation_engine_test.cpp:575
Expected equality of these values:
  std::any_cast<std::string>(result->getField("full_name"))
    Which is: "John"
  "John Smith"
]]></failure>
    </testcase>
    <testcase name="ConditionalTransformationInEngine" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="581" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.541" classname="TransformationEngineTest" />
    <testcase name="DirectDateTransformationException" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="644" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.543" classname="TransformationEngineTest" />
    <testcase name="BatchTransformationWithErrors" file="/workspace/tests/unit/transform/transformation_engine_test.cpp" line="671" status="run" result="completed" time="0.003" timestamp="2025-08-02T17:20:31.544" classname="TransformationEngineTest">
      <failure message="/workspace/tests/unit/transform/transformation_engine_test.cpp:722&#x0A;Expected equality of these values:&#x0A;  std::any_cast&lt;std::string&gt;(result_batch.getRecord(0).getField(&quot;birth_datetime&quot;))&#x0A;    Which is: &quot;1990-01-15&quot;&#x0A;  &quot;1990-01-15 00:00:00&quot;&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/transformation_engine_test.cpp:722
Expected equality of these values:
  std::any_cast<std::string>(result_batch.getRecord(0).getField("birth_datetime"))
    Which is: "1990-01-15"
  "1990-01-15 00:00:00"
]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="TransformationMemoryManagementTest" tests="8" failures="0" disabled="0" skipped="0" errors="0" time="4.22" timestamp="2025-08-02T17:20:31.547">
    <testcase name="TransformationLifecycle" file="/workspace/tests/unit/transform/transformation_memory_management_test.cpp" line="55" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.547" classname="TransformationMemoryManagementTest" />
    <testcase name="EngineLifecycleLargeConfig" file="/workspace/tests/unit/transform/transformation_memory_management_test.cpp" line="76" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:31.549" classname="TransformationMemoryManagementTest" />
    <testcase name="RecordBatchMemoryManagement" file="/workspace/tests/unit/transform/transformation_memory_management_test.cpp" line="110" status="run" result="completed" time="2.945" timestamp="2025-08-02T17:20:31.550" classname="TransformationMemoryManagementTest" />
    <testcase name="TransformationCacheMemoryBounds" file="/workspace/tests/unit/transform/transformation_memory_management_test.cpp" line="151" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:34.496" classname="TransformationMemoryManagementTest" />
    <testcase name="ConcurrentTransformationMemory" file="/workspace/tests/unit/transform/transformation_memory_management_test.cpp" line="183" status="run" result="completed" time="1.077" timestamp="2025-08-02T17:20:34.497" classname="TransformationMemoryManagementTest" />
    <testcase name="ExceptionSafetyResourceCleanup" file="/workspace/tests/unit/transform/transformation_memory_management_test.cpp" line="245" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:35.575" classname="TransformationMemoryManagementTest" />
    <testcase name="FieldMetadataMemoryManagement" file="/workspace/tests/unit/transform/transformation_memory_management_test.cpp" line="308" status="run" result="completed" time="0.055" timestamp="2025-08-02T17:20:35.576" classname="TransformationMemoryManagementTest" />
    <testcase name="VocabularyServiceMemoryManagement" file="/workspace/tests/unit/transform/transformation_memory_management_test.cpp" line="366" status="run" result="completed" time="0.137" timestamp="2025-08-02T17:20:35.631" classname="TransformationMemoryManagementTest" />
  </testsuite>
  <testsuite name="TransformationPipelineComprehensiveTest" tests="5" failures="0" disabled="0" skipped="0" errors="0" time="1.635" timestamp="2025-08-02T17:20:35.768">
    <testcase name="CompleteTransformationPipeline" file="/workspace/tests/unit/transform/transformation_pipeline_comprehensive_test.cpp" line="151" status="run" result="completed" time="0.003" timestamp="2025-08-02T17:20:35.768" classname="TransformationPipelineComprehensiveTest" />
    <testcase name="ErrorHandlingAcrossTransformations" file="/workspace/tests/unit/transform/transformation_pipeline_comprehensive_test.cpp" line="179" status="run" result="completed" time="0.005" timestamp="2025-08-02T17:20:35.772" classname="TransformationPipelineComprehensiveTest" />
    <testcase name="TransformationPerformance" file="/workspace/tests/unit/transform/transformation_pipeline_comprehensive_test.cpp" line="242" status="run" result="completed" time="1.615" timestamp="2025-08-02T17:20:35.777" classname="TransformationPipelineComprehensiveTest" />
    <testcase name="TransformationMetadata" file="/workspace/tests/unit/transform/transformation_pipeline_comprehensive_test.cpp" line="284" status="run" result="completed" time="0.006" timestamp="2025-08-02T17:20:37.393" classname="TransformationPipelineComprehensiveTest" />
    <testcase name="ComplexValidationScenarios" file="/workspace/tests/unit/transform/transformation_pipeline_comprehensive_test.cpp" line="329" status="run" result="completed" time="0.003" timestamp="2025-08-02T17:20:37.400" classname="TransformationPipelineComprehensiveTest" />
  </testsuite>
  <testsuite name="TransformationRegistryTest" tests="10" failures="0" disabled="0" skipped="0" errors="0" time="0.007" timestamp="2025-08-02T17:20:37.404">
    <testcase name="RegisterTransformation" file="/workspace/tests/unit/transform/transformation_registry_test.cpp" line="48" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.404" classname="TransformationRegistryTest" />
    <testcase name="CreateRegisteredTransformation" file="/workspace/tests/unit/transform/transformation_registry_test.cpp" line="57" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.404" classname="TransformationRegistryTest" />
    <testcase name="CreateUnregisteredTransformationThrows" file="/workspace/tests/unit/transform/transformation_registry_test.cpp" line="68" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.404" classname="TransformationRegistryTest" />
    <testcase name="HasTransformationRegistered" file="/workspace/tests/unit/transform/transformation_registry_test.cpp" line="76" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.404" classname="TransformationRegistryTest" />
    <testcase name="HasTransformationUnregistered" file="/workspace/tests/unit/transform/transformation_registry_test.cpp" line="84" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.404" classname="TransformationRegistryTest" />
    <testcase name="GetRegisteredTypes" file="/workspace/tests/unit/transform/transformation_registry_test.cpp" line="89" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.404" classname="TransformationRegistryTest" />
    <testcase name="ThreadSafetyRegistration" file="/workspace/tests/unit/transform/transformation_registry_test.cpp" line="105" status="run" result="completed" time="0.005" timestamp="2025-08-02T17:20:37.404" classname="TransformationRegistryTest" />
    <testcase name="ThreadSafetyCreation" file="/workspace/tests/unit/transform/transformation_registry_test.cpp" line="140" status="run" result="completed" time="0.001" timestamp="2025-08-02T17:20:37.410" classname="TransformationRegistryTest" />
    <testcase name="OverwriteExistingRegistration" file="/workspace/tests/unit/transform/transformation_registry_test.cpp" line="178" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.411" classname="TransformationRegistryTest" />
    <testcase name="DebugListRegisteredTransformations" file="/workspace/tests/unit/transform/transformation_registry_test.cpp" line="199" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.411" classname="TransformationRegistryTest" />
  </testsuite>
  <testsuite name="UKLocalizedTransformTest" tests="16" failures="0" disabled="0" skipped="1" errors="0" time="0.002" timestamp="2025-08-02T17:20:37.411">
    <testcase name="CelsiusTemperatureConversionUK" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="73" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.411" classname="UKLocalizedTransformTest" />
    <testcase name="CustomJavaScriptUKDateFormatting" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="91" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.412" classname="UKLocalizedTransformTest" />
    <testcase name="CurrencyFormattingUKPounds" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="110" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="DateTransformationUKFormat" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="127" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="DateTransformationUKTimeZone" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="146" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="HealthcareNHSNumberValidation" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="166" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="MedicationDosageUKUnits" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="183" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="NumericFormattingUKThousandSeparators" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="201" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="PostcodeValidationUK" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="217" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="PythonTransformationUKCalculations" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="244" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="SQLTransformationUKHealthcareData" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="261" status="run" result="skipped" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest">
      <skipped message="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp:263&#x0A;SQL transformation does not support simple string literals&#x0A;"><![CDATA[/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp:263
SQL transformation does not support simple string literals
]]></skipped>
    </testcase>
    <testcase name="StringConcatenationUKAddress" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="267" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="StringTransformationUKPhoneNumbers" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="294" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="TemperatureConversionMedicalUK" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="311" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="VocabularyTransformationReadCodes" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="336" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.413" classname="UKLocalizedTransformTest" />
    <testcase name="WeightConversionStonesToKilograms" file="/workspace/tests/unit/transform/uk_localized_comprehensive_test.cpp" line="360" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.414" classname="UKLocalizedTransformTest" />
  </testsuite>
  <testsuite name="ValidationEngineTest" tests="15" failures="0" disabled="0" skipped="0" errors="0" time="0.001" timestamp="2025-08-02T17:20:37.414">
    <testcase name="ValidationEngineInitialization" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="32" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.414" classname="ValidationEngineTest" />
    <testcase name="RegisterCustomValidator" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="41" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.414" classname="ValidationEngineTest" />
    <testcase name="LoadValidationRules" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="79" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.414" classname="ValidationEngineTest" />
    <testcase name="ValidateRecord" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="101" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.414" classname="ValidationEngineTest" />
    <testcase name="ValidateField" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="121" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.414" classname="ValidationEngineTest" />
    <testcase name="RequiredFieldValidator" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="143" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.414" classname="ValidationEngineTest" />
    <testcase name="DataTypeValidator" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="165" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.414" classname="ValidationEngineTest" />
    <testcase name="DataTypeValidatorDateFormat" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="184" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.414" classname="ValidationEngineTest" />
    <testcase name="RangeValidator" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="208" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.414" classname="ValidationEngineTest" />
    <testcase name="PatternValidator" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="231" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.414" classname="ValidationEngineTest" />
    <testcase name="LengthValidator" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="250" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.415" classname="ValidationEngineTest" />
    <testcase name="CustomValidator" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="273" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.415" classname="ValidationEngineTest" />
    <testcase name="ClearRules" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="300" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.415" classname="ValidationEngineTest" />
    <testcase name="MultipleValidatorsPerField" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="320" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.415" classname="ValidationEngineTest" />
    <testcase name="ComplexValidationScenario" file="/workspace/tests/unit/transform/validation_engine_test.cpp" line="350" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.415" classname="ValidationEngineTest" />
  </testsuite>
  <testsuite name="VocabularyServiceTest" tests="31" failures="1" disabled="0" skipped="0" errors="0" time="0.026" timestamp="2025-08-02T17:20:37.415">
    <testcase name="InitializeService" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="252" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:37.415" classname="VocabularyServiceTest" />
    <testcase name="GetConceptById" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="261" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.418" classname="VocabularyServiceTest" />
    <testcase name="GetNonExistentConcept" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="273" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.418" classname="VocabularyServiceTest" />
    <testcase name="GetConceptByCode" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="279" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.418" classname="VocabularyServiceTest" />
    <testcase name="LoadMappingsFromConfig" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="288" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.418" classname="VocabularyServiceTest" />
    <testcase name="CaseInsensitiveMapping" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="308" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.419" classname="VocabularyServiceTest" />
    <testcase name="GetMappings" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="323" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.419" classname="VocabularyServiceTest" />
    <testcase name="GetStandardConcept" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="344" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.419" classname="VocabularyServiceTest" />
    <testcase name="GetDescendants" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="350" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.419" classname="VocabularyServiceTest" />
    <testcase name="GetAncestors" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="356" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.419" classname="VocabularyServiceTest" />
    <testcase name="IsInDomain" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="362" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.420" classname="VocabularyServiceTest" />
    <testcase name="CacheOperations" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="368" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.420" classname="VocabularyServiceTest" />
    <testcase name="ClearCache" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="385" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.420" classname="VocabularyServiceTest" />
    <testcase name="VocabularyValidator" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="403" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.420" classname="VocabularyServiceTest" />
    <testcase name="VocabularyValidatorMappingExists" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="418" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.420" classname="VocabularyServiceTest" />
    <testcase name="VocabularyValidatorStandardConcept" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="432" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.420" classname="VocabularyServiceTest" />
    <testcase name="VocabularyValidatorGetErrors" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="440" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.420" classname="VocabularyServiceTest" />
    <testcase name="VocabularyServiceManagerSingleton" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="449" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.420" classname="VocabularyServiceTest" />
    <testcase name="VocabularyServiceManagerNotInitialized" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="462" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.420" classname="VocabularyServiceTest" />
    <testcase name="ThreadSafety" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="474" status="run" result="completed" time="0.006" timestamp="2025-08-02T17:20:37.420" classname="VocabularyServiceTest" />
    <testcase name="ConceptCreation" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="511" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.427" classname="VocabularyServiceTest" />
    <testcase name="AddMappingWithContext" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="536" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.427" classname="VocabularyServiceTest" />
    <testcase name="AutoLearnRecordUnrecognizedTerms" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="557" status="run" result="completed" time="0.002" timestamp="2025-08-02T17:20:37.427" classname="VocabularyServiceTest" />
    <testcase name="AutoLearnAddMedicalDictionaries" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="573" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.429" classname="VocabularyServiceTest" />
    <testcase name="AutoLearnProcessTerms" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="608" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.429" classname="VocabularyServiceTest" />
    <testcase name="UKMedicalTermIdentification" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="626" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.430" classname="VocabularyServiceTest" />
    <testcase name="UKUnitsAndMeasurements" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="641" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.430" classname="VocabularyServiceTest" />
    <testcase name="UKPostalCodeValidation" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="664" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.430" classname="VocabularyServiceTest" />
    <testcase name="UKDateFormatHandling" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="680" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.430" classname="VocabularyServiceTest" />
    <testcase name="AutoLearnThreadSafety" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="706" status="run" result="completed" time="0.01" timestamp="2025-08-02T17:20:37.430" classname="VocabularyServiceTest">
      <failure message="/workspace/tests/unit/transform/vocabulary_service_test.cpp:732&#x0A;Expected equality of these values:&#x0A;  num_threads * terms_per_thread&#x0A;    Which is: 100&#x0A;  unrecognized.size()&#x0A;    Which is: 0&#x0A;" type=""><![CDATA[/workspace/tests/unit/transform/vocabulary_service_test.cpp:732
Expected equality of these values:
  num_threads * terms_per_thread
    Which is: 100
  unrecognized.size()
    Which is: 0
]]></failure>
    </testcase>
    <testcase name="SQLInjectionProtection" file="/workspace/tests/unit/transform/vocabulary_service_test.cpp" line="737" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.441" classname="VocabularyServiceTest" />
  </testsuite>
  <testsuite name="VocabularyTransformationsTest" tests="20" failures="0" disabled="0" skipped="0" errors="0" time="0.001" timestamp="2025-08-02T17:20:37.442">
    <testcase name="ConceptHierarchyToAncestor" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="351" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.442" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptHierarchyToDescendant" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="370" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.442" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptHierarchyAllDescendants" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="390" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.442" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptHierarchyToRoot" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="412" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.442" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptHierarchyNoMapping" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="430" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.442" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptHierarchyDefaultConcept" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="449" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.442" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptHierarchyStringInput" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="468" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.442" classname="VocabularyTransformationsTest" />
    <testcase name="DomainMappingBasic" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="487" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.442" classname="VocabularyTransformationsTest" />
    <testcase name="DomainMappingWrongDomain" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="506" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
    <testcase name="DomainMappingToStandard" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="524" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
    <testcase name="DomainMappingWithDefaults" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="541" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
    <testcase name="DomainMappingNoMapping" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="561" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptRelationshipMapsTo" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="579" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptRelationshipNoMatch" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="596" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptRelationshipWithFilters" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="614" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptRelationshipStringInput" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="632" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
    <testcase name="VocabularyServiceNotInitialized" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="647" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptHierarchyInvalidConceptId" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="667" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
    <testcase name="InvalidInputType" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="684" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
    <testcase name="ConceptHierarchyToLeaf" file="/workspace/tests/unit/transform/vocabulary_transformations_test.cpp" line="700" status="run" result="completed" time="0." timestamp="2025-08-02T17:20:37.443" classname="VocabularyTransformationsTest" />
  </testsuite>
</testsuites>
