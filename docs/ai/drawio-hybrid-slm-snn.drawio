<mxfile host="app.diagrams.net">
  <diagram name="Hybrid-SLM-SNN-Architecture" id="hybrid-arch-10k">
    <mxGraphModel dx="2000" dy="1200" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1920" pageHeight="1080" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- Main System Container -->
        <mxCell id="2" value="£10,000 HYBRID SLM-SNN HARDWARE ARCHITECTURE" style="rounded=1;whiteSpace=wrap;html=1;fontSize=22;fontStyle=1;fillColor=#f8fafc;strokeColor=#334155;strokeWidth=3;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="1840" height="1000" as="geometry"/>
        </mxCell>
        
        <!-- CPU Section -->
        <mxCell id="3" value="AMD EPYC 7443P&#xa;24-Core CPU&#xa;£1,750&#xa;&#xa;200W TDP&#xa;2.85 GHz Base&#xa;4.0 GHz Boost" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;fillColor=#3B82F6;strokeColor=#1e40af;strokeWidth=3;fontColor=#ffffff;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="860" y="120" width="240" height="160" as="geometry"/>
        </mxCell>
        
        <!-- PCIe Controller Hub -->
        <mxCell id="4" value="PCIe 4.0 Controller&#xa;128 Lanes Total&#xa;31.5 GB/s per x16" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#dbeafe;strokeColor=#2563eb;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="870" y="340" width="220" height="80" as="geometry"/>
        </mxCell>
        
        <!-- NVIDIA RTX 4090 -->
        <mxCell id="5" value="NVIDIA RTX 4090&#xa;24GB GDDR6X VRAM&#xa;£1,600&#xa;&#xa;450W TDP&#xa;16,384 CUDA Cores&#xa;82.58 TFLOPS FP32&#xa;SLM Training/Inference" style="rounded=1;whiteSpace=wrap;html=1;fontSize=15;fontStyle=1;fillColor=#22C55E;strokeColor=#15803d;strokeWidth=3;fontColor=#ffffff;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="200" y="500" width="260" height="180" as="geometry"/>
        </mxCell>
        
        <!-- Mythic M1076 AMP -->
        <mxCell id="6" value="Mythic M1076 AMP&#xa;Analog Matrix Processor&#xa;£3,000&#xa;&#xa;3W TDP&#xa;25.6 TOPS @ INT8&#xa;108 FPS ResNet-50&#xa;Edge AI Acceleration" style="rounded=1;whiteSpace=wrap;html=1;fontSize=15;fontStyle=1;fillColor=#8B5CF6;strokeColor=#6b21a8;strokeWidth=3;fontColor=#ffffff;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="540" y="500" width="260" height="180" as="geometry"/>
        </mxCell>
        
        <!-- Intel Arria 10 FPGA -->
        <mxCell id="7" value="Intel Arria 10 FPGA&#xa;GX 1150&#xa;£2,500&#xa;&#xa;70W TDP&#xa;1M Logic Elements&#xa;1.5 TFLOPS&#xa;Universal Acceleration" style="rounded=1;whiteSpace=wrap;html=1;fontSize=15;fontStyle=1;fillColor=#F97316;strokeColor=#c2410c;strokeWidth=3;fontColor=#ffffff;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="1160" y="500" width="260" height="180" as="geometry"/>
        </mxCell>
        
        <!-- Intel Loihi 2 -->
        <mxCell id="8" value="Intel Loihi 2&#xa;Neuromorphic Processor&#xa;(Research Access)&#xa;&#xa;0.1W TDP&#xa;1M Neurons&#xa;120M Synapses&#xa;SNN Processing" style="rounded=1;whiteSpace=wrap;html=1;fontSize=15;fontStyle=1;fillColor=#14B8A6;strokeColor=#0f766e;strokeWidth=3;fontColor=#ffffff;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="1500" y="500" width="260" height="180" as="geometry"/>
        </mxCell>
        
        <!-- Memory -->
        <mxCell id="9" value="64GB DDR4 ECC&#xa;3200MHz&#xa;£400&#xa;&#xa;Bandwidth: 51.2 GB/s&#xa;8-Channel" style="rounded=1;whiteSpace=wrap;html=1;fontSize=13;fillColor=#6B7280;strokeColor=#374151;strokeWidth=2;fontColor=#ffffff;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1200" y="160" width="180" height="100" as="geometry"/>
        </mxCell>
        
        <!-- Storage -->
        <mxCell id="10" value="2TB NVMe SSD&#xa;PCIe Gen4 x4&#xa;£150&#xa;&#xa;7 GB/s Read&#xa;5 GB/s Write" style="rounded=1;whiteSpace=wrap;html=1;fontSize=13;fillColor=#6B7280;strokeColor=#374151;strokeWidth=2;fontColor=#ffffff;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1440" y="160" width="180" height="100" as="geometry"/>
        </mxCell>
        
        <!-- PCIe Lane Connections -->
        <mxCell id="11" value="PCIe 4.0 x16&#xa;31.5 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=5;strokeColor=#16a34a;fontSize=14;fontStyle=1;startArrow=none;endArrow=block;endFill=1;" edge="1" parent="1" source="4" target="5">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="12" value="PCIe 3.0 x4&#xa;3.94 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#7c3aed;fontSize=14;fontStyle=1;startArrow=none;endArrow=block;endFill=1;" edge="1" parent="1" source="4" target="6">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="13" value="PCIe 4.0 x8&#xa;15.75 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#ea580c;fontSize=14;fontStyle=1;startArrow=none;endArrow=block;endFill=1;" edge="1" parent="1" source="4" target="7">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="14" value="PCIe 3.0 x1&#xa;0.985 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#0891b2;fontSize=14;fontStyle=1;startArrow=none;endArrow=block;endFill=1;" edge="1" parent="1" source="4" target="8">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- CPU to Components -->
        <mxCell id="15" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#3B82F6;" edge="1" parent="1" source="3" target="4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="16" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#6b7280;" edge="1" parent="1" source="3" target="9">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="17" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#6b7280;" edge="1" parent="1" source="3" target="10">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Power Supply Unit -->
        <mxCell id="18" value="1000W Platinum PSU&#xa;£200&#xa;90% Efficiency&#xa;Modular Cables" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#dc2626;strokeColor=#991b1b;strokeWidth=2;fontColor=#ffffff;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="800" width="200" height="100" as="geometry"/>
        </mxCell>
        
        <!-- Cooling System -->
        <mxCell id="19" value="360mm Liquid Cooling&#xa;£300&#xa;3x120mm Fans&#xa;Dual Pump Design" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#0ea5e9;strokeColor=#0369a1;strokeWidth=2;fontColor=#ffffff;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="360" y="800" width="200" height="100" as="geometry"/>
        </mxCell>
        
        <!-- Motherboard -->
        <mxCell id="20" value="Server Motherboard&#xa;SP3 Socket&#xa;£600&#xa;8x PCIe Slots" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#475569;strokeColor=#1e293b;strokeWidth=2;fontColor=#ffffff;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="800" width="200" height="100" as="geometry"/>
        </mxCell>
        
        <!-- Chassis -->
        <mxCell id="21" value="4U Rackmount Chassis&#xa;£200&#xa;10 Drive Bays&#xa;Redundant Fans" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#94a3b8;strokeColor=#475569;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="840" y="800" width="200" height="100" as="geometry"/>
        </mxCell>
        
        <!-- Power Distribution Header -->
        <mxCell id="22" value="POWER DISTRIBUTION & THERMAL MANAGEMENT" style="rounded=0;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;fillColor=#fef3c7;strokeColor=#f59e0b;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="120" y="740" width="920" height="40" as="geometry"/>
        </mxCell>
        
        <!-- Total Power Budget -->
        <mxCell id="23" value="TOTAL SYSTEM POWER&#xa;&#xa;Peak Load: ~723.1W&#xa;• CPU: 200W&#xa;• GPU: 450W&#xa;• FPGA: 70W&#xa;• Mythic: 3W&#xa;• Loihi 2: 0.1W&#xa;&#xa;Power Headroom: 277W (28%)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#fee2e2;strokeColor=#dc2626;strokeWidth=3;align=left;verticalAlign=top;spacing=5;" vertex="1" parent="1">
          <mxGeometry x="1080" y="760" width="280" height="160" as="geometry"/>
        </mxCell>
        
        <!-- Expansion Capabilities -->
        <mxCell id="24" value="EXPANSION SLOTS&#xa;&#xa;Available:&#xa;• 2x PCIe 4.0 x16&#xa;• 3x PCIe 4.0 x8&#xa;• 4x M.2 NVMe&#xa;• 2x U.2 NVMe&#xa;&#xa;Future Upgrades:&#xa;• Additional GPUs&#xa;• CXL Memory&#xa;• 100GbE NIC" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;align=left;fillColor=#e0e7ff;strokeColor=#6366f1;strokeWidth=3;fontStyle=1;verticalAlign=top;spacing=5;" vertex="1" parent="1">
          <mxGeometry x="1400" y="760" width="220" height="160" as="geometry"/>
        </mxCell>
        
        <!-- System Summary -->
        <mxCell id="25" value="SYSTEM SPECIFICATIONS SUMMARY&#xa;&#xa;Total Cost: £9,950 (£50 under budget)&#xa;&#xa;Compute Performance:&#xa;• AI/ML: 25.6 TOPS (Mythic) + 82.58 TFLOPS (RTX 4090)&#xa;• FPGA: 1.5 TFLOPS + Custom Acceleration&#xa;• Neuromorphic: 1M Neurons / 120M Synapses&#xa;&#xa;Memory & Storage:&#xa;• System Memory: 64GB DDR4 @ 51.2 GB/s&#xa;• GPU Memory: 24GB GDDR6X @ 1008 GB/s&#xa;• Storage: 2TB NVMe @ 7 GB/s Read&#xa;&#xa;Connectivity:&#xa;• Total PCIe 4.0 Lanes: 128&#xa;• Network Ready: 10/25/100 GbE Capable" style="rounded=1;whiteSpace=wrap;html=1;fontSize=13;fontStyle=0;align=left;fillColor=#f0fdf4;strokeColor=#22c55e;strokeWidth=3;verticalAlign=top;spacing=5;" vertex="1" parent="1">
          <mxGeometry x="120" y="940" width="600" height="240" as="geometry"/>
        </mxCell>
        
        <!-- Thermal Zone 1 -->
        <mxCell id="26" value="Thermal Zone 1: CPU + Memory (Max 85°C)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fillColor=none;strokeColor=#f59e0b;strokeWidth=2;dashed=1;dashPattern=8 8;opacity=60;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="840" y="100" width="560" height="190" as="geometry"/>
        </mxCell>
        
        <!-- Thermal Zone 2 -->
        <mxCell id="27" value="Thermal Zone 2: High-Power GPUs (Max 83°C)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fillColor=none;strokeColor=#f59e0b;strokeWidth=2;dashed=1;dashPattern=8 8;opacity=60;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="180" y="480" width="300" height="220" as="geometry"/>
        </mxCell>
        
        <!-- Thermal Zone 3 -->
        <mxCell id="28" value="Thermal Zone 3: Low-Power Processors" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fillColor=none;strokeColor=#f59e0b;strokeWidth=2;dashed=1;dashPattern=8 8;opacity=60;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="520" y="480" width="760" height="220" as="geometry"/>
        </mxCell>
        
        <!-- Future CXL Connection -->
        <mxCell id="29" value="Future CXL 2.0&#xa;Memory Coherency" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#9ca3af;fontSize=12;fontStyle=2;dashed=1;dashPattern=8 8;startArrow=none;endArrow=block;endFill=1;" edge="1" parent="1" source="6" target="8">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="670" y="720"/>
              <mxPoint x="1630" y="720"/>
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Processing Capabilities Box -->
        <mxCell id="30" value="HYBRID PROCESSING CAPABILITIES&#xa;&#xa;1. Small Language Models (SLMs)&#xa;   • RTX 4090: Training & Inference&#xa;   • 24GB VRAM for 7B-13B Models&#xa;&#xa;2. Edge AI Inference&#xa;   • Mythic M1076: Ultra-Low Power&#xa;   • 8.5 TOPS/W Efficiency&#xa;&#xa;3. Custom Acceleration&#xa;   • Arria 10 FPGA: Flexible Pipeline&#xa;   • Custom Kernels & Algorithms&#xa;&#xa;4. Neuromorphic Computing&#xa;   • Loihi 2: Event-Driven Processing&#xa;   • Spiking Neural Networks&#xa;&#xa;5. General Compute&#xa;   • EPYC 7443P: 24 Cores&#xa;   • Data Preprocessing & Orchestration" style="rounded=1;whiteSpace=wrap;html=1;fontSize=13;fontStyle=1;align=left;fillColor=#ede9fe;strokeColor=#7c3aed;strokeWidth=3;verticalAlign=top;spacing=5;" vertex="1" parent="1">
          <mxGeometry x="760" y="940" width="400" height="300" as="geometry"/>
        </mxCell>
        
        <!-- Network Interface Placeholder -->
        <mxCell id="31" value="Network Interface&#xa;(Optional)&#xa;10/25/100 GbE&#xa;PCIe 4.0 x8" style="rounded=1;whiteSpace=wrap;html=1;fontSize=13;fillColor=#e7e5e4;strokeColor=#78716c;strokeWidth=2;fontStyle=2;dashed=1;dashPattern=5 5;" vertex="1" parent="1">
          <mxGeometry x="1660" y="800" width="160" height="80" as="geometry"/>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>