<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-01T00:00:00.000Z" agent="5.0" etag="slm-snn-architecture" version="21.1.2" type="device">
  <diagram name="Four-Layer Hybrid SLM-SNN Architecture" id="slm-snn-architecture">
    <mxGraphModel dx="1434" dy="834" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1000" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- Title -->
        <mxCell id="title" value="Four-Layer Hybrid SLM-SNN System Architecture" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="20" width="800" height="40" as="geometry"/>
        </mxCell>
        
        <!-- Sensor Layer -->
        <mxCell id="sensor-layer" value="SENSOR LAYER" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6B7280;fontColor=#FFFFFF;strokeColor=#374151;fontStyle=1;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="100" y="800" width="1400" height="120" as="geometry"/>
        </mxCell>
        
        <mxCell id="ecg-sensor" value="ECG Sensor&lt;br&gt;1000Hz&lt;br&gt;8 channels&lt;br&gt;16 KB/s" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9CA3AF;fontColor=#FFFFFF;strokeColor=#6B7280;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="150" y="820" width="140" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="camera-array" value="Camera Array&lt;br&gt;60 FPS&lt;br&gt;4K Resolution&lt;br&gt;1.5 GB/s" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9CA3AF;fontColor=#FFFFFF;strokeColor=#6B7280;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="380" y="820" width="140" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="lidar" value="LIDAR&lt;br&gt;100Hz&lt;br&gt;128 channels&lt;br&gt;20 MB/s" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9CA3AF;fontColor=#FFFFFF;strokeColor=#6B7280;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="610" y="820" width="140" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="temp-sensors" value="Temperature&lt;br&gt;Sensors&lt;br&gt;500Hz&lt;br&gt;2 KB/s" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9CA3AF;fontColor=#FFFFFF;strokeColor=#6B7280;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="840" y="820" width="140" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="multi-modal" value="Multi-modal&lt;br&gt;Inputs&lt;br&gt;Variable Rate&lt;br&gt;100 MB/s" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9CA3AF;fontColor=#FFFFFF;strokeColor=#6B7280;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1070" y="820" width="140" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="sensor-bandwidth" value="Total Bandwidth: 1.62 GB/s" style="text;html=1;strokeColor=none;fillColor=#FEF3C7;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1300" y="850" width="180" height="30" as="geometry"/>
        </mxCell>
        
        <!-- FPGA Universal Accelerator Layer -->
        <mxCell id="fpga-layer" value="FPGA UNIVERSAL ACCELERATOR - Intel Arria 10" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#8B5CF6;fontColor=#FFFFFF;strokeColor=#6D28D9;fontStyle=1;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="100" y="620" width="1400" height="140" as="geometry"/>
        </mxCell>
        
        <mxCell id="dsp-pipeline" value="Custom DSP&lt;br&gt;Pipelines&lt;br&gt;Parallel Processing&lt;br&gt;0.1ms latency" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#A78BFA;fontColor=#FFFFFF;strokeColor=#7C3AED;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="200" y="640" width="160" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="data-routing" value="Intelligent&lt;br&gt;Data Routing&lt;br&gt;Load Balancing&lt;br&gt;QoS Management" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#A78BFA;fontColor=#FFFFFF;strokeColor=#7C3AED;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="450" y="640" width="160" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="preprocessing" value="Real-time&lt;br&gt;Preprocessing&lt;br&gt;Feature Extraction&lt;br&gt;Noise Reduction" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#A78BFA;fontColor=#FFFFFF;strokeColor=#7C3AED;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="700" y="640" width="160" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="spike-encoding" value="Spike Train&lt;br&gt;Encoding&lt;br&gt;Event Generation&lt;br&gt;10 GSPS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#A78BFA;fontColor=#FFFFFF;strokeColor=#7C3AED;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="950" y="640" width="160" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="fpga-power" value="Power: 25W" style="text;html=1;strokeColor=none;fillColor=#DDD6FE;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1200" y="670" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="fpga-throughput" value="Throughput: 400 Gbps" style="text;html=1;strokeColor=none;fillColor=#DDD6FE;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1340" y="670" width="140" height="30" as="geometry"/>
        </mxCell>
        
        <!-- SNN Reflexive Layer -->
        <mxCell id="snn-layer" value="SNN REFLEXIVE LAYER - Intel Loihi 2 Clusters" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#10B981;fontColor=#FFFFFF;strokeColor=#059669;fontStyle=1;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="100" y="420" width="1400" height="160" as="geometry"/>
        </mxCell>
        
        <mxCell id="emergency-response" value="Emergency&lt;br&gt;Response Module&lt;br&gt;0.5ms latency&lt;br&gt;Critical Reflexes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#34D399;fontColor=#FFFFFF;strokeColor=#10B981;fontSize=12;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="150" y="440" width="160" height="120" as="geometry"/>
        </mxCell>
        
        <mxCell id="pattern-detection" value="Real-time&lt;br&gt;Pattern Detection&lt;br&gt;Anomaly Detection&lt;br&gt;1ms response" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#34D399;fontColor=#FFFFFF;strokeColor=#10B981;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="380" y="440" width="160" height="120" as="geometry"/>
        </mxCell>
        
        <mxCell id="motor-control" value="Motor Control&lt;br&gt;Pathways&lt;br&gt;Reflex Arcs&lt;br&gt;Sub-ms precision" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#34D399;fontColor=#FFFFFF;strokeColor=#10B981;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="610" y="440" width="160" height="120" as="geometry"/>
        </mxCell>
        
        <mxCell id="sensory-fusion" value="Multi-modal&lt;br&gt;Sensory Fusion&lt;br&gt;Spike Processing&lt;br&gt;128M synapses" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#34D399;fontColor=#FFFFFF;strokeColor=#10B981;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="840" y="440" width="160" height="120" as="geometry"/>
        </mxCell>
        
        <mxCell id="learning-module" value="Online Learning&lt;br&gt;STDP Rules&lt;br&gt;Adaptive Weights&lt;br&gt;Plasticity" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#34D399;fontColor=#FFFFFF;strokeColor=#10B981;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1070" y="440" width="160" height="120" as="geometry"/>
        </mxCell>
        
        <mxCell id="snn-power" value="Power: 0.1W per chip" style="text;html=1;strokeColor=none;fillColor=#D1FAE5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1300" y="490" width="160" height="30" as="geometry"/>
        </mxCell>
        
        <!-- SLM Cognitive Layer -->
        <mxCell id="slm-layer" value="SLM COGNITIVE LAYER - Domain-Specific Models" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#3B82F6;fontColor=#FFFFFF;strokeColor=#1E40AF;fontStyle=1;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="100" y="220" width="1400" height="160" as="geometry"/>
        </mxCell>
        
        <mxCell id="medical-slm" value="Medical SLM&lt;br&gt;SNOMED CT&lt;br&gt;ICD-10 Processing&lt;br&gt;50ms reasoning" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#60A5FA;fontColor=#FFFFFF;strokeColor=#2563EB;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="200" y="240" width="160" height="120" as="geometry"/>
        </mxCell>
        
        <mxCell id="vehicle-slm" value="Vehicle SLM&lt;br&gt;OpenDRIVE&lt;br&gt;Path Planning&lt;br&gt;40ms decisions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#60A5FA;fontColor=#FFFFFF;strokeColor=#2563EB;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="450" y="240" width="160" height="120" as="geometry"/>
        </mxCell>
        
        <mxCell id="financial-slm" value="Financial SLM&lt;br&gt;FIX Protocol&lt;br&gt;Risk Analysis&lt;br&gt;30ms processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#60A5FA;fontColor=#FFFFFF;strokeColor=#2563EB;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="700" y="240" width="160" height="120" as="geometry"/>
        </mxCell>
        
        <mxCell id="industrial-slm" value="Industrial SLM&lt;br&gt;OPC UA&lt;br&gt;Predictive Maint.&lt;br&gt;60ms analysis" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#60A5FA;fontColor=#FFFFFF;strokeColor=#2563EB;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="950" y="240" width="160" height="120" as="geometry"/>
        </mxCell>
        
        <mxCell id="slm-power" value="Power: 15W per model" style="text;html=1;strokeColor=none;fillColor=#DBEAFE;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1200" y="280" width="160" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="slm-accuracy" value="Accuracy: 99.2%" style="text;html=1;strokeColor=none;fillColor=#DBEAFE;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1200" y="320" width="160" height="30" as="geometry"/>
        </mxCell>
        
        <!-- Application Layer -->
        <mxCell id="app-layer" value="APPLICATION LAYER" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F59E0B;fontColor=#FFFFFF;strokeColor=#D97706;fontStyle=1;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="100" y="80" width="1400" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="healthcare-app" value="Healthcare&lt;br&gt;Dashboard&lt;br&gt;Real-time Vitals" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FCD34D;fontColor=#78350F;strokeColor=#F59E0B;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="200" y="95" width="140" height="70" as="geometry"/>
        </mxCell>
        
        <mxCell id="vehicle-control" value="Autonomous&lt;br&gt;Vehicle Controls&lt;br&gt;Navigation UI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FCD34D;fontColor=#78350F;strokeColor=#F59E0B;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="450" y="95" width="140" height="70" as="geometry"/>
        </mxCell>
        
        <mxCell id="trading-system" value="Trading&lt;br&gt;Systems&lt;br&gt;Risk Dashboard" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FCD34D;fontColor=#78350F;strokeColor=#F59E0B;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="700" y="95" width="140" height="70" as="geometry"/>
        </mxCell>
        
        <mxCell id="safety-systems" value="Industrial&lt;br&gt;Safety Systems&lt;br&gt;Alert Platform" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FCD34D;fontColor=#78350F;strokeColor=#F59E0B;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="950" y="95" width="140" height="70" as="geometry"/>
        </mxCell>
        
        <mxCell id="monitoring" value="Unified&lt;br&gt;Monitoring&lt;br&gt;Interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FCD34D;fontColor=#78350F;strokeColor=#F59E0B;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1200" y="95" width="140" height="70" as="geometry"/>
        </mxCell>
        
        <!-- Data Flow Arrows -->
        <mxCell id="sensor-to-fpga" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#374151;startArrow=none;endArrow=classic;endFill=1;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="800" y="800" as="sourcePoint"/>
            <mxPoint x="800" y="760" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow-label-1" value="1.62 GB/s" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontStyle=1;fillColor=#FEF3C7;strokeColor=none;" vertex="1" connectable="0" parent="sensor-to-fpga">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint x="40" as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="fpga-to-snn" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#6D28D9;startArrow=none;endArrow=classic;endFill=1;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="600" y="620" as="sourcePoint"/>
            <mxPoint x="600" y="580" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow-label-2" value="Spike Trains&lt;br&gt;10 GSPS" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontStyle=1;fillColor=#DDD6FE;strokeColor=none;" vertex="1" connectable="0" parent="fpga-to-snn">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint x="60" as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="fpga-to-slm" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#6D28D9;startArrow=none;endArrow=classic;endFill=1;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1000" y="620" as="sourcePoint"/>
            <mxPoint x="1000" y="380" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow-label-3" value="Preprocessed&lt;br&gt;Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontStyle=1;fillColor=#DDD6FE;strokeColor=none;" vertex="1" connectable="0" parent="fpga-to-slm">
          <mxGeometry x="-0.3" y="-1" relative="1" as="geometry">
            <mxPoint x="60" y="-60" as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="snn-to-slm" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#059669;startArrow=none;endArrow=classic;endFill=1;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="800" y="420" as="sourcePoint"/>
            <mxPoint x="800" y="380" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow-label-4" value="Feature Maps" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontStyle=1;fillColor=#D1FAE5;strokeColor=none;" vertex="1" connectable="0" parent="snn-to-slm">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint x="50" as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="slm-to-app" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#1E40AF;startArrow=none;endArrow=classic;endFill=1;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="800" y="220" as="sourcePoint"/>
            <mxPoint x="800" y="180" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow-label-5" value="Decisions&lt;br&gt;50ms" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontStyle=1;fillColor=#DBEAFE;strokeColor=none;" vertex="1" connectable="0" parent="slm-to-app">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint x="40" as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <!-- Emergency Override Path -->
        <mxCell id="emergency-path" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#DC2626;startArrow=none;endArrow=classic;endFill=1;dashed=1;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="230" y="440" as="sourcePoint"/>
            <mxPoint x="230" y="180" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="emergency-label" value="EMERGENCY&lt;br&gt;OVERRIDE&lt;br&gt;0.5ms" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontStyle=1;fillColor=#FEE2E2;strokeColor=none;fontColor=#DC2626;" vertex="1" connectable="0" parent="emergency-path">
          <mxGeometry x="-0.3" y="-1" relative="1" as="geometry">
            <mxPoint x="-60" as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <!-- Feedback Loops -->
        <mxCell id="feedback-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#059669;startArrow=classic;endArrow=none;startFill=1;dashed=1;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="400" y="380" as="sourcePoint"/>
            <mxPoint x="400" y="420" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="feedback-label-1" value="Learning&lt;br&gt;Feedback" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontStyle=2;fillColor=#D1FAE5;strokeColor=none;" vertex="1" connectable="0" parent="feedback-1">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint x="-50" as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="feedback-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#1E40AF;startArrow=classic;endArrow=none;startFill=1;dashed=1;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1100" y="180" as="sourcePoint"/>
            <mxPoint x="1100" y="220" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="feedback-label-2" value="Control&lt;br&gt;Feedback" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontStyle=2;fillColor=#DBEAFE;strokeColor=none;" vertex="1" connectable="0" parent="feedback-2">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint x="50" as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <!-- Latency Indicators -->
        <mxCell id="latency-total" value="End-to-End Latency: 51.6ms (normal) | 0.5ms (emergency)" style="text;html=1;strokeColor=none;fillColor=#FEF9C3;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="550" y="950" width="500" height="30" as="geometry"/>
        </mxCell>
        
        <!-- Energy Consumption Summary -->
        <mxCell id="energy-box" value="Total System Power Consumption" style="swimlane;fontStyle=1;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;fillColor=#1F2937;fontColor=#FFFFFF;strokeColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="1300" y="820" width="250" height="150" as="geometry"/>
        </mxCell>
        
        <mxCell id="energy-1" value="Sensor Layer: 5W" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;portConstraint=eastwest;rotatable=0;fontColor=#FFFFFF;" vertex="1" parent="energy-box">
          <mxGeometry y="30" width="250" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="energy-2" value="FPGA Layer: 25W" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;portConstraint=eastwest;rotatable=0;fontColor=#FFFFFF;" vertex="1" parent="energy-box">
          <mxGeometry y="50" width="250" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="energy-3" value="SNN Layer: 0.5W (5 chips)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;portConstraint=eastwest;rotatable=0;fontColor=#FFFFFF;" vertex="1" parent="energy-box">
          <mxGeometry y="70" width="250" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="energy-4" value="SLM Layer: 60W (4 models)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;portConstraint=eastwest;rotatable=0;fontColor=#FFFFFF;" vertex="1" parent="energy-box">
          <mxGeometry y="90" width="250" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="energy-5" value="Application Layer: 10W" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;portConstraint=eastwest;rotatable=0;fontColor=#FFFFFF;" vertex="1" parent="energy-box">
          <mxGeometry y="110" width="250" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="energy-total" value="TOTAL: 100.5W" style="text;strokeColor=none;fillColor=#F59E0B;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;portConstraint=eastwest;rotatable=0;fontColor=#FFFFFF;fontStyle=1;" vertex="1" parent="energy-box">
          <mxGeometry y="130" width="250" height="20" as="geometry"/>
        </mxCell>
        
        <!-- Fault Tolerance Indicators -->
        <mxCell id="fault-1" value="❌" style="text;html=1;strokeColor=none;fillColor=#FCA5A5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="120" y="460" width="25" height="25" as="geometry"/>
        </mxCell>
        
        <mxCell id="fault-2" value="❌" style="text;html=1;strokeColor=none;fillColor=#FCA5A5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="120" y="260" width="25" height="25" as="geometry"/>
        </mxCell>
        
        <mxCell id="fault-label" value="Fault Tolerance&lt;br&gt;Pathways" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="360" width="80" height="30" as="geometry"/>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>