<mxfile host="app.diagrams.net" modified="2025-01-01T00:00:00.000Z" agent="5.0" version="24.7.5" etag="HU-Integration" type="device">
  <diagram name="HU-Integration" id="hybrid-unit-integration">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title-1" value="HYBRID UNIT (HU) INTEGRATION WITH EMERGENCY OVERRIDE &amp; ARBITRATION LOGIC" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontFamily=Arial;" vertex="1" parent="1">
          <mxGeometry x="400" y="20" width="800" height="40" as="geometry" />
        </mxCell>
        
        <!-- SLM Cognitive Engine -->
        <mxCell id="slm-1" value="SLM COGNITIVE ENGINE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3B82F6;strokeColor=#2563EB;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="200" width="280" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="slm-2" value="Medical Treatment&lt;br&gt;Recommendations" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DBEAFE;strokeColor=#3B82F6;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="80" y="280" width="130" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="slm-3" value="Driving&lt;br&gt;Maneuvers" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DBEAFE;strokeColor=#3B82F6;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="230" y="280" width="130" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="slm-4" value="Industrial Process&lt;br&gt;Adjustments" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DBEAFE;strokeColor=#3B82F6;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="155" y="350" width="130" height="50" as="geometry" />
        </mxCell>
        
        <!-- SNN Reflexive Engine -->
        <mxCell id="snn-1" value="SNN REFLEXIVE ENGINE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=#059669;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="600" width="280" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="snn-2" value="Cardiac Arrest&lt;br&gt;Detection" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#10B981;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="80" y="680" width="130" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="snn-3" value="Collision&lt;br&gt;Avoidance" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#10B981;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="230" y="680" width="130" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="snn-4" value="Equipment&lt;br&gt;Failure Alerts" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#10B981;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="155" y="750" width="130" height="50" as="geometry" />
        </mxCell>
        
        <!-- Hybrid Unit Arbitration Logic -->
        <mxCell id="hu-1" value="HYBRID UNIT ARBITRATION LOGIC" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#8B5CF6;strokeColor=#7C3AED;fontColor=#FFFFFF;fontSize=16;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="400" width="400" height="80" as="geometry" />
        </mxCell>
        
        <!-- Priority Matrix -->
        <mxCell id="pm-1" value="PRIORITY MATRIX" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#EDE9FE;strokeColor=#8B5CF6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="480" y="520" width="160" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="pm-2" value="P1: Life-Critical" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F3E8FF;strokeColor=#8B5CF6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="480" y="570" width="160" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="pm-3" value="P2: Safety-Critical" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F3E8FF;strokeColor=#8B5CF6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="480" y="610" width="160" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="pm-4" value="P3: Mission-Critical" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F3E8FF;strokeColor=#8B5CF6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="480" y="650" width="160" height="30" as="geometry" />
        </mxCell>
        
        <!-- Confidence Scoring -->
        <mxCell id="cs-1" value="CONFIDENCE SCORING" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#EDE9FE;strokeColor=#8B5CF6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="680" y="520" width="160" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="cs-2" value="SLM: 0.95 (50ms)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DBEAFE;strokeColor=#3B82F6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="680" y="570" width="160" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="cs-3" value="SNN: 0.87 (0.5ms)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#10B981;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="680" y="610" width="160" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="cs-4" value="Threshold: 0.85" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F3E8FF;strokeColor=#8B5CF6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="680" y="650" width="160" height="30" as="geometry" />
        </mxCell>
        
        <!-- State Machine -->
        <mxCell id="sm-1" value="ARBITRATION STATE MACHINE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#EDE9FE;strokeColor=#8B5CF6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="880" y="520" width="200" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="sm-2" value="NORMAL" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E0E7FF;strokeColor=#8B5CF6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="890" y="580" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="sm-3" value="ALERT" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FEF3C7;strokeColor=#F59E0B;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="990" y="580" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="sm-4" value="EMERGENCY" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#EF4444;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="940" y="640" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Emergency Override Pathway -->
        <mxCell id="eop-1" value="EMERGENCY OVERRIDE PATHWAY" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#EF4444;strokeColor=#DC2626;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="1200" y="300" width="300" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="eop-2" value="BYPASS NORMAL PROCESSING" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#EF4444;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1200" y="380" width="300" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="eop-3" value="SUB-MILLISECOND RESPONSE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#EF4444;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1200" y="440" width="300" height="40" as="geometry" />
        </mxCell>
        
        <!-- Shared Memory Pool -->
        <mxCell id="smp-1" value="SHARED MEMORY POOL" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#F3F4F6;strokeColor=#6B7280;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="420" y="280" width="140" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="smp-2" value="10GB/s&lt;br&gt;BANDWIDTH" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="330" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Decision Tree -->
        <mxCell id="dt-1" value="DECISION LOGIC" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#EDE9FE;strokeColor=#8B5CF6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1120" y="520" width="140" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="dt-2" value="Emergency?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#EF4444;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1140" y="580" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="dt-3" value="Priority &gt; P1?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#F3E8FF;strokeColor=#8B5CF6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1140" y="660" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="dt-4" value="Conf &gt; 0.85?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#E0E7FF;strokeColor=#8B5CF6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1140" y="740" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Output -->
        <mxCell id="output-1" value="SYSTEM OUTPUT" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#374151;strokeColor=#1F2937;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="700" y="850" width="200" height="60" as="geometry" />
        </mxCell>
        
        <!-- Timing Diagrams -->
        <mxCell id="td-1" value="TIMING CONSTRAINTS" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F9FAFB;strokeColor=#6B7280;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="880" width="400" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="td-2" value="SNN Response: 0.5ms" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#10B981;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="80" y="930" width="190" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="td-3" value="SLM Response: 50ms" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DBEAFE;strokeColor=#3B82F6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="290" y="930" width="190" height="30" as="geometry" />
        </mxCell>
        
        <!-- Fault Tolerance -->
        <mxCell id="ft-1" value="FAULT TOLERANCE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F9FAFB;strokeColor=#6B7280;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="980" width="400" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ft-2" value="Redundant Processing" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F3F4F6;strokeColor=#6B7280;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="80" y="1030" width="120" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="ft-3" value="Watchdog Timer" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F3F4F6;strokeColor=#6B7280;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="220" y="1030" width="120" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="ft-4" value="Fallback Mode" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F3F4F6;strokeColor=#6B7280;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="360" y="1030" width="120" height="30" as="geometry" />
        </mxCell>
        
        <!-- Connections -->
        <!-- SLM to Shared Memory -->
        <mxCell id="c1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#3B82F6;strokeWidth=2;endArrow=classic;endFill=1;" edge="1" parent="1" source="slm-1" target="smp-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- SNN to Shared Memory -->
        <mxCell id="c2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#10B981;strokeWidth=2;endArrow=classic;endFill=1;" edge="1" parent="1" source="snn-1" target="smp-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Shared Memory to HU -->
        <mxCell id="c3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#6B7280;strokeWidth=2;endArrow=classic;endFill=1;" edge="1" parent="1" source="smp-1" target="hu-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- HU to Priority Matrix -->
        <mxCell id="c4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#8B5CF6;strokeWidth=2;endArrow=classic;endFill=1;" edge="1" parent="1" source="hu-1" target="pm-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- HU to Confidence Scoring -->
        <mxCell id="c5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#8B5CF6;strokeWidth=2;endArrow=classic;endFill=1;" edge="1" parent="1" source="hu-1" target="cs-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- HU to State Machine -->
        <mxCell id="c6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#8B5CF6;strokeWidth=2;endArrow=classic;endFill=1;" edge="1" parent="1" source="hu-1" target="sm-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Emergency Override Connection -->
        <mxCell id="c7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#EF4444;strokeWidth=3;endArrow=classic;endFill=1;dashed=1;" edge="1" parent="1" source="snn-1" target="eop-1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="220" y="560" />
              <mxPoint x="1350" y="560" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- State Transitions -->
        <mxCell id="c8" style="edgeStyle=orthogonalEdgeStyle;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#8B5CF6;strokeWidth=1;endArrow=classic;endFill=1;" edge="1" parent="1" source="sm-2" target="sm-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="c9" style="edgeStyle=orthogonalEdgeStyle;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#F59E0B;strokeWidth=1;endArrow=classic;endFill=1;" edge="1" parent="1" source="sm-3" target="sm-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Decision Tree Flow -->
        <mxCell id="c10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#8B5CF6;strokeWidth=1;endArrow=classic;endFill=1;" edge="1" parent="1" source="dt-1" target="dt-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="c11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#8B5CF6;strokeWidth=1;endArrow=classic;endFill=1;" edge="1" parent="1" source="dt-2" target="dt-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="c12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#8B5CF6;strokeWidth=1;endArrow=classic;endFill=1;" edge="1" parent="1" source="dt-3" target="dt-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Output Connections -->
        <mxCell id="c13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#374151;strokeWidth=2;endArrow=classic;endFill=1;" edge="1" parent="1" source="hu-1" target="output-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Emergency to Output -->
        <mxCell id="c14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#EF4444;strokeWidth=3;endArrow=classic;endFill=1;dashed=1;" edge="1" parent="1" source="eop-3" target="output-1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1350" y="880" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Feedback Loops -->
        <mxCell id="fb-1" value="FEEDBACK" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;rotation=-90;" vertex="1" parent="1">
          <mxGeometry x="1040" y="700" width="80" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="c15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#6B7280;strokeWidth=1;endArrow=classic;endFill=1;dashed=1;startArrow=classic;startFill=1;" edge="1" parent="1" source="output-1" target="hu-1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="800" y="820" />
              <mxPoint x="1060" y="820" />
              <mxPoint x="1060" y="440" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="legend-1" value="LEGEND" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F9FAFB;strokeColor=#6B7280;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1300" y="700" width="200" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-2" value="Cognitive Path" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DBEAFE;strokeColor=#3B82F6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1300" y="750" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-3" value="Reflexive Path" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#10B981;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1410" y="750" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-4" value="Arbitration Logic" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#EDE9FE;strokeColor=#8B5CF6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1300" y="790" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-5" value="Emergency Override" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#EF4444;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1410" y="790" width="90" height="30" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>