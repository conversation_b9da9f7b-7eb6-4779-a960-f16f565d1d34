<mxfile host="app.diagrams.net">
  <diagram name="SLM-SNN Market Disruption" id="market-disruption">
    <mxGraphModel dx="1422" dy="782" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Background Panels -->
        <mxCell id="panel-left" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FEE2CC;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="480" height="820" as="geometry" />
        </mxCell>
        
        <mxCell id="panel-center" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E6E6FA;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="540" y="40" width="480" height="820" as="geometry" />
        </mxCell>
        
        <mxCell id="panel-right" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D4E1F5;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="1040" y="40" width="480" height="820" as="geometry" />
        </mxCell>
        
        <!-- Headers -->
        <mxCell id="header-left" value="Traditional LLM vs SLM-SNN Hybrid" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="60" width="480" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="header-center" value="Domain-Specific Deployments" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="540" y="60" width="480" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="header-right" value="2025-2030 Adoption Timeline" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1040" y="60" width="480" height="40" as="geometry" />
        </mxCell>
        
        <!-- Left Panel Content -->
        <mxCell id="llm-limitations" value="Traditional LLM Limitations" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F97316;strokeColor=#C73500;fontColor=#FFFFFF;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="60" y="120" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="energy-high" value="Energy Consumption&lt;br&gt;500W-1000W per query" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FED7AA;strokeColor=#F97316;" vertex="1" parent="1">
          <mxGeometry x="60" y="200" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="latency-high" value="Latency&lt;br&gt;50-500ms response time" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FED7AA;strokeColor=#F97316;" vertex="1" parent="1">
          <mxGeometry x="60" y="280" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="privacy-concerns" value="Privacy Concerns&lt;br&gt;Cloud-dependent processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FED7AA;strokeColor=#F97316;" vertex="1" parent="1">
          <mxGeometry x="60" y="360" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="cost-high" value="System Cost&lt;br&gt;£100,000+" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FED7AA;strokeColor=#F97316;" vertex="1" parent="1">
          <mxGeometry x="60" y="440" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="snn-advantages" value="SLM-SNN Advantages" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=#16A34A;fontColor=#FFFFFF;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="120" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="energy-low" value="95% Energy Reduction&lt;br&gt;25W-50W per query" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBF7D0;strokeColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="300" y="200" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="latency-low" value="Ultra-Low Latency&lt;br&gt;0.5ms reflexive response" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBF7D0;strokeColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="300" y="280" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="privacy-local" value="Privacy-Preserving&lt;br&gt;100% local processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBF7D0;strokeColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="300" y="360" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="cost-low" value="80% Cost Reduction&lt;br&gt;£10,000 systems" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBF7D0;strokeColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="300" y="440" width="200" height="60" as="geometry" />
        </mxCell>
        
        <!-- Comparison Arrows -->
        <mxCell id="arrow1" value="" style="shape=flexArrow;endArrow=classic;html=1;fillColor=#10B981;strokeColor=#16A34A;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="265" y="230" as="sourcePoint" />
            <mxPoint x="295" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow2" value="" style="shape=flexArrow;endArrow=classic;html=1;fillColor=#10B981;strokeColor=#16A34A;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="265" y="310" as="sourcePoint" />
            <mxPoint x="295" y="310" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow3" value="" style="shape=flexArrow;endArrow=classic;html=1;fillColor=#10B981;strokeColor=#16A34A;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="265" y="390" as="sourcePoint" />
            <mxPoint x="295" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow4" value="" style="shape=flexArrow;endArrow=classic;html=1;fillColor=#10B981;strokeColor=#16A34A;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="265" y="470" as="sourcePoint" />
            <mxPoint x="295" y="470" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Energy Comparison Chart -->
        <mxCell id="energy-chart" value="Energy Consumption Comparison" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="60" y="540" width="440" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="energy-bar-llm" value="LLM: 750W avg" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F97316;strokeColor=#C73500;fontColor=#FFFFFF;" vertex="1" parent="1">
          <mxGeometry x="60" y="580" width="400" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="energy-bar-snn" value="SNN: 37.5W" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=#16A34A;fontColor=#FFFFFF;" vertex="1" parent="1">
          <mxGeometry x="60" y="630" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Center Panel Content -->
        <mxCell id="healthcare-domain" value="Healthcare - SNOMED CT" style="swimlane;startSize=40;fillColor=#6366F1;strokeColor=#4338CA;fontColor=#FFFFFF;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="120" width="440" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="healthcare-content" value="• Real-time patient monitoring with 0.5ms response&lt;br&gt;• Local processing ensures HIPAA compliance&lt;br&gt;• 400,000+ medical concepts processed on-device&lt;br&gt;• Emergency detection with spike-based alerting&lt;br&gt;• 95% reduction in cloud dependency" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="healthcare-domain">
          <mxGeometry x="10" y="50" width="420" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="automotive-domain" value="Automotive - OpenDRIVE" style="swimlane;startSize=40;fillColor=#8B5CF6;strokeColor=#7C3AED;fontColor=#FFFFFF;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="340" width="440" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="automotive-content" value="• Autonomous driving with sub-millisecond reflexes&lt;br&gt;• HD map processing with neuromorphic efficiency&lt;br&gt;• Sensor fusion: LiDAR, camera, radar integration&lt;br&gt;• 99.99% uptime with local edge processing&lt;br&gt;• Energy-efficient operation for EVs" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="automotive-domain">
          <mxGeometry x="10" y="50" width="420" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="iot-domain" value="Industrial IoT" style="swimlane;startSize=40;fillColor=#3B82F6;strokeColor=#2563EB;fontColor=#FFFFFF;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="560" width="440" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="iot-content" value="• Predictive maintenance with anomaly detection&lt;br&gt;• 1M+ sensor data points processed per second&lt;br&gt;• Zero-latency quality control systems&lt;br&gt;• Distributed intelligence across factory floor&lt;br&gt;• 80% reduction in false positive alerts" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="iot-domain">
          <mxGeometry x="10" y="50" width="420" height="140" as="geometry" />
        </mxCell>
        
        <!-- Right Panel Content -->
        <mxCell id="timeline-2025" value="2025" style="ellipse;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=#16A34A;fontColor=#FFFFFF;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1060" y="140" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="timeline-2025-content" value="Early Adopters&lt;br&gt;• Research labs&lt;br&gt;• Tech startups&lt;br&gt;• 5% market penetration" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E0F2FE;strokeColor=#0284C7;" vertex="1" parent="1">
          <mxGeometry x="1200" y="120" width="300" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="timeline-2027" value="2027" style="ellipse;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=#16A34A;fontColor=#FFFFFF;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1060" y="320" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="timeline-2027-content" value="Enterprise Adoption&lt;br&gt;• Healthcare systems&lt;br&gt;• Automotive OEMs&lt;br&gt;• 25% market penetration" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E0F2FE;strokeColor=#0284C7;" vertex="1" parent="1">
          <mxGeometry x="1200" y="300" width="300" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="timeline-2030" value="2030" style="ellipse;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=#16A34A;fontColor=#FFFFFF;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1060" y="500" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="timeline-2030-content" value="Market Maturity&lt;br&gt;• Consumer devices&lt;br&gt;• Edge AI standard&lt;br&gt;• 60% market penetration" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E0F2FE;strokeColor=#0284C7;" vertex="1" parent="1">
          <mxGeometry x="1200" y="480" width="300" height="100" as="geometry" />
        </mxCell>
        
        <!-- Timeline Connectors -->
        <mxCell id="timeline-connector1" value="" style="endArrow=classic;html=1;strokeWidth=3;strokeColor=#10B981;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1110" y="200" as="sourcePoint" />
            <mxPoint x="1110" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="timeline-connector2" value="" style="endArrow=classic;html=1;strokeWidth=3;strokeColor=#10B981;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1110" y="380" as="sourcePoint" />
            <mxPoint x="1110" y="500" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- ROI Metrics -->
        <mxCell id="roi-box" value="ROI Projections" style="swimlane;startSize=30;fillColor=#FBBF24;strokeColor=#F59E0B;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1060" y="620" width="440" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="roi-content" value="• Energy cost savings: £85,000/year per deployment&lt;br&gt;• Implementation cost: 80% lower than LLM systems&lt;br&gt;• Payback period: 14 months average&lt;br&gt;• Total 5-year savings: £425,000 per system" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="roi-box">
          <mxGeometry x="10" y="40" width="420" height="90" as="geometry" />
        </mxCell>
        
        <!-- Key Benefits Summary -->
        <mxCell id="benefits-title" value="Hybrid System Competitive Advantages" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="60" y="700" width="440" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="benefit1" value="Real-time Processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=#16A34A;fontColor=#FFFFFF;" vertex="1" parent="1">
          <mxGeometry x="60" y="740" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="benefit2" value="Edge-Native" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=#16A34A;fontColor=#FFFFFF;" vertex="1" parent="1">
          <mxGeometry x="170" y="740" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="benefit3" value="Privacy-First" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=#16A34A;fontColor=#FFFFFF;" vertex="1" parent="1">
          <mxGeometry x="280" y="740" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="benefit4" value="Cost-Effective" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=#16A34A;fontColor=#FFFFFF;" vertex="1" parent="1">
          <mxGeometry x="390" y="740" width="110" height="40" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>