<?xml version="1.0" encoding="UTF-8"?>
<testsuites tests="138" failures="0" disabled="24" errors="0" time="9.368" timestamp="2025-08-02T16:50:17.937" name="AllTests">
  <testsuite name="JsonBatchLoaderTest" tests="8" failures="0" disabled="0" skipped="0" errors="0" time="2.456" timestamp="2025-08-02T16:50:17.937">
    <testcase name="BasicFunctionality" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="65" status="run" result="completed" time="0.217" timestamp="2025-08-02T16:50:17.937" classname="JsonBatchLoaderTest" />
    <testcase name="DifferentOptions" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="87" status="run" result="completed" time="0.317" timestamp="2025-08-02T16:50:18.154" classname="JsonBatchLoaderTest" />
    <testcase name="ArrayOutputFormat" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="79" status="run" result="completed" time="0.223" timestamp="2025-08-02T16:50:18.471" classname="JsonBatchLoaderTest" />
    <testcase name="NdjsonOutputFormat" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="97" status="run" result="completed" time="0.317" timestamp="2025-08-02T16:50:18.695" classname="JsonBatchLoaderTest" />
    <testcase name="ComplexDataTypes" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="124" status="run" result="completed" time="0.226" timestamp="2025-08-02T16:50:19.012" classname="JsonBatchLoaderTest" />
    <testcase name="MetadataInclusion" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="163" status="run" result="completed" time="0.362" timestamp="2025-08-02T16:50:19.238" classname="JsonBatchLoaderTest" />
    <testcase name="ConcurrentWriting" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="210" status="run" result="completed" time="0.321" timestamp="2025-08-02T16:50:19.600" classname="JsonBatchLoaderTest" />
    <testcase name="PerformanceTest" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="254" status="run" result="completed" time="0.47" timestamp="2025-08-02T16:50:19.922" classname="JsonBatchLoaderTest" />
  </testsuite>
  <testsuite name="HttpLoaderTest" tests="4" failures="0" disabled="0" skipped="0" errors="0" time="0." timestamp="2025-08-02T16:50:20.393">
    <testcase name="BasicFunctionality" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="128" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.393" classname="HttpLoaderTest" />
    <testcase name="BasicConfiguration" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="312" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.393" classname="HttpLoaderTest" />
    <testcase name="AuthenticationConfiguration" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="323" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.393" classname="HttpLoaderTest" />
    <testcase name="RetryMechanism" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="338" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.393" classname="HttpLoaderTest" />
  </testsuite>
  <testsuite name="S3LoaderTest" tests="5" failures="0" disabled="0" skipped="0" errors="0" time="0.002" timestamp="2025-08-02T16:50:20.394">
    <testcase name="BasicFunctionality" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="158" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.394" classname="S3LoaderTest" />
    <testcase name="BasicConfiguration" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="420" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.394" classname="S3LoaderTest" />
    <testcase name="MultipartUploadConfiguration" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="430" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.395" classname="S3LoaderTest" />
    <testcase name="MetadataAndTags" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="446" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.395" classname="S3LoaderTest" />
    <testcase name="ServerSideEncryption" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="471" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.396" classname="S3LoaderTest" />
  </testsuite>
  <testsuite name="AdditionalLoadersErrorTest" tests="2" failures="0" disabled="0" skipped="0" errors="0" time="0.225" timestamp="2025-08-02T16:50:20.396">
    <testcase name="InvalidConfigurations" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="171" status="run" result="completed" time="0.118" timestamp="2025-08-02T16:50:20.396" classname="AdditionalLoadersErrorTest" />
    <testcase name="InvalidConfigurationsComplex" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="487" status="run" result="completed" time="0.106" timestamp="2025-08-02T16:50:20.515" classname="AdditionalLoadersErrorTest" />
  </testsuite>
  <testsuite name="AdditionalLoadersTest" tests="7" failures="0" disabled="0" skipped="0" errors="0" time="0.415" timestamp="2025-08-02T16:50:20.622">
    <testcase name="CompilationTest" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="185" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.622" classname="AdditionalLoadersTest" />
    <testcase name="JsonBatchLoaderInstantiation" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="193" status="run" result="completed" time="0.101" timestamp="2025-08-02T16:50:20.622" classname="AdditionalLoadersTest" />
    <testcase name="JsonBatchLoaderUKLocalization" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="205" status="run" result="completed" time="0.21" timestamp="2025-08-02T16:50:20.723" classname="AdditionalLoadersTest" />
    <testcase name="HttpLoaderUKRegion" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="250" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.934" classname="AdditionalLoadersTest" />
    <testcase name="DatabaseLoaderUKNHSData" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="268" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.934" classname="AdditionalLoadersTest" />
    <testcase name="CompilationTestComplex" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="753" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:20.934" classname="AdditionalLoadersTest" />
    <testcase name="JsonBatchLoaderInstantiationComplex" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="762" status="run" result="completed" time="0.102" timestamp="2025-08-02T16:50:20.934" classname="AdditionalLoadersTest" />
  </testsuite>
  <testsuite name="JsonBatchLoaderUKTest" tests="2" failures="0" disabled="0" skipped="0" errors="0" time="0.311" timestamp="2025-08-02T16:50:21.037">
    <testcase name="UKDateFormatting" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="628" status="run" result="completed" time="0.172" timestamp="2025-08-02T16:50:21.037" classname="JsonBatchLoaderUKTest" />
    <testcase name="UKMeasurementAndCurrency" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="653" status="run" result="completed" time="0.138" timestamp="2025-08-02T16:50:21.210" classname="JsonBatchLoaderUKTest" />
  </testsuite>
  <testsuite name="UKLocalizationTest" tests="3" failures="0" disabled="0" skipped="0" errors="0" time="0.572" timestamp="2025-08-02T16:50:21.348">
    <testcase name="UKDataValidation" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="674" status="run" result="completed" time="0.007" timestamp="2025-08-02T16:50:21.348" classname="UKLocalizationTest" />
    <testcase name="LargeUKDatasetMemoryManagement" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="686" status="run" result="completed" time="0.317" timestamp="2025-08-02T16:50:21.356" classname="UKLocalizationTest" />
    <testcase name="UKDataPerformanceBenchmark" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="715" status="run" result="completed" time="0.246" timestamp="2025-08-02T16:50:21.674" classname="UKLocalizationTest" />
  </testsuite>
  <testsuite name="EnhancedBatchTest" tests="9" failures="0" disabled="0" skipped="0" errors="0" time="0.001" timestamp="2025-08-02T16:50:21.921">
    <testcase name="ConstructorInitialization" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="47" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:21.921" classname="EnhancedBatchTest" />
    <testcase name="AddRecord" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="55" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:21.921" classname="EnhancedBatchTest" />
    <testcase name="BatchBecomesFull" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="69" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:21.921" classname="EnhancedBatchTest" />
    <testcase name="ClearBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="84" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:21.921" classname="EnhancedBatchTest" />
    <testcase name="SortBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="99" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:21.921" classname="EnhancedBatchTest" />
    <testcase name="DeduplicateBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="127" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:21.921" classname="EnhancedBatchTest" />
    <testcase name="CompressBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="152" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:21.921" classname="EnhancedBatchTest" />
    <testcase name="UnsupportedCompressionThrows" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="169" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:21.922" classname="EnhancedBatchTest" />
    <testcase name="EstimateMemoryUsage" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="178" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:21.922" classname="EnhancedBatchTest" />
  </testsuite>
  <testsuite name="BatchLoaderTest" tests="15" failures="0" disabled="0" skipped="0" errors="0" time="3.025" timestamp="2025-08-02T16:50:21.922">
    <testcase name="Initialization" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="216" status="run" result="completed" time="0.106" timestamp="2025-08-02T16:50:21.922" classname="BatchLoaderTest" />
    <testcase name="LoadSingleRecord" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="230" status="run" result="completed" time="0.101" timestamp="2025-08-02T16:50:22.029" classname="BatchLoaderTest" />
    <testcase name="BatchFillingAndProcessing" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="243" status="run" result="completed" time="0.31" timestamp="2025-08-02T16:50:22.130" classname="BatchLoaderTest" />
    <testcase name="LoadBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="266" status="run" result="completed" time="0.306" timestamp="2025-08-02T16:50:22.441" classname="BatchLoaderTest" />
    <testcase name="CommitFlushesCurrentBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="292" status="run" result="completed" time="0.107" timestamp="2025-08-02T16:50:22.748" classname="BatchLoaderTest" />
    <testcase name="RollbackClearsCurrentBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="313" status="run" result="completed" time="0.104" timestamp="2025-08-02T16:50:22.856" classname="BatchLoaderTest" />
    <testcase name="LoadErrorHandling" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="331" status="run" result="completed" time="0.103" timestamp="2025-08-02T16:50:22.962" classname="BatchLoaderTest" />
    <testcase name="GetStatistics" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="345" status="run" result="completed" time="0.109" timestamp="2025-08-02T16:50:23.066" classname="BatchLoaderTest" />
    <testcase name="BatchLoaderWithDeduplication" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="468" status="run" result="completed" time="0.213" timestamp="2025-08-02T16:50:23.181" classname="BatchLoaderTest" />
    <testcase name="BatchLoaderWithSorting" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="501" status="run" result="completed" time="0.218" timestamp="2025-08-02T16:50:23.395" classname="BatchLoaderTest" />
    <testcase name="FlushIntervalFunctionality" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="531" status="run" result="completed" time="0.417" timestamp="2025-08-02T16:50:23.614" classname="BatchLoaderTest" />
    <testcase name="DestructorHandlesCleanup" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="567" status="run" result="completed" time="0.209" timestamp="2025-08-02T16:50:24.032" classname="BatchLoaderTest" />
    <testcase name="ConcurrentBatchAccess" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="698" status="run" result="completed" time="0.111" timestamp="2025-08-02T16:50:24.241" classname="BatchLoaderTest" />
    <testcase name="MemoryManagement" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="742" status="run" result="completed" time="0.28" timestamp="2025-08-02T16:50:24.353" classname="BatchLoaderTest" />
    <testcase name="BatchCompression" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="801" status="run" result="completed" time="0.314" timestamp="2025-08-02T16:50:24.633" classname="BatchLoaderTest" />
  </testsuite>
  <testsuite name="CsvBatchLoaderTest" tests="5" failures="0" disabled="0" skipped="0" errors="0" time="1.13" timestamp="2025-08-02T16:50:24.948">
    <testcase name="Initialization" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="383" status="run" result="completed" time="0.109" timestamp="2025-08-02T16:50:24.948" classname="CsvBatchLoaderTest" />
    <testcase name="CsvOutputWithHeader" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="394" status="run" result="completed" time="0.211" timestamp="2025-08-02T16:50:25.058" classname="CsvBatchLoaderTest" />
    <testcase name="CsvEscaping" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="424" status="run" result="completed" time="0.244" timestamp="2025-08-02T16:50:25.269" classname="CsvBatchLoaderTest" />
    <testcase name="CustomDelimiter" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="589" status="run" result="completed" time="0.349" timestamp="2025-08-02T16:50:25.514" classname="CsvBatchLoaderTest" />
    <testcase name="ErrorScenarios" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="779" status="run" result="completed" time="0.215" timestamp="2025-08-02T16:50:25.863" classname="CsvBatchLoaderTest" />
  </testsuite>
  <testsuite name="BatchLoaderFactoryTest" tests="2" failures="0" disabled="0" skipped="0" errors="0" time="0.104" timestamp="2025-08-02T16:50:26.079">
    <testcase name="CreateCsvBatchLoader" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="450" status="run" result="completed" time="0.103" timestamp="2025-08-02T16:50:26.079" classname="BatchLoaderFactoryTest" />
    <testcase name="UnknownLoaderTypeThrows" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="461" status="run" result="completed" time="0.001" timestamp="2025-08-02T16:50:26.182" classname="BatchLoaderFactoryTest" />
  </testsuite>
  <testsuite name="MmapBatchLoaderTest" tests="15" failures="0" disabled="12" skipped="0" errors="0" time="0.658" timestamp="2025-08-02T16:50:26.183">
    <testcase name="Initialization" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="641" status="run" result="completed" time="0.116" timestamp="2025-08-02T16:50:26.183" classname="MmapBatchLoaderTest" />
    <testcase name="MemoryMappedFileOperations" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="651" status="run" result="completed" time="0.22" timestamp="2025-08-02T16:50:26.299" classname="MmapBatchLoaderTest" />
    <testcase name="FileExtension" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="673" status="run" result="completed" time="0.322" timestamp="2025-08-02T16:50:26.520" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_BasicInitialization" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="62" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_SmallInitialSize" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="103" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_AutomaticFileExtension" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="127" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_ConcurrentAccess" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="155" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_WriteFailureHandling" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="210" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_PlatformSpecificBehavior" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="220" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_MappingFailureRecovery" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="250" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_BatchProcessing" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="282" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_MemoryUsageTracking" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="302" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_FileSynchronization" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="328" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_DiskFullHandling" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="363" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_PerformanceComparison" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="397" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
  </testsuite>
  <testsuite name="BulkInsertBufferTest" tests="3" failures="0" disabled="0" skipped="0" errors="0" time="0." timestamp="2025-08-02T16:50:26.842">
    <testcase name="ConstructionAndBasicOperations" file="/workspace/tests/unit/load/database_loader_test.cpp" line="86" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.842" classname="BulkInsertBufferTest" />
    <testcase name="AddRecords" file="/workspace/tests/unit/load/database_loader_test.cpp" line="93" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.842" classname="BulkInsertBufferTest" />
    <testcase name="ClearBuffer" file="/workspace/tests/unit/load/database_loader_test.cpp" line="119" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.843" classname="BulkInsertBufferTest" />
  </testsuite>
  <testsuite name="DatabaseLoaderTest" tests="15" failures="0" disabled="8" skipped="0" errors="0" time="0.003" timestamp="2025-08-02T16:50:26.843">
    <testcase name="Initialization" file="/workspace/tests/unit/load/database_loader_test.cpp" line="158" status="run" result="completed" time="0.001" timestamp="2025-08-02T16:50:26.843" classname="DatabaseLoaderTest" />
    <testcase name="InitializationWithMissingTableThrows" file="/workspace/tests/unit/load/database_loader_test.cpp" line="176" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.844" classname="DatabaseLoaderTest" />
    <testcase name="LoadSingleRecord" file="/workspace/tests/unit/load/database_loader_test.cpp" line="187" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.844" classname="DatabaseLoaderTest" />
    <testcase name="BulkInsert" file="/workspace/tests/unit/load/database_loader_test.cpp" line="218" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.845" classname="DatabaseLoaderTest" />
    <testcase name="LoadWithMetadataTable" file="/workspace/tests/unit/load/database_loader_test.cpp" line="262" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.845" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_LoadBatch" file="/workspace/tests/unit/load/database_loader_test.cpp" line="289" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_CommitFlushesBuffers" file="/workspace/tests/unit/load/database_loader_test.cpp" line="319" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="RollbackClearsBuffers" file="/workspace/tests/unit/load/database_loader_test.cpp" line="345" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.845" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_Finalization" file="/workspace/tests/unit/load/database_loader_test.cpp" line="369" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="TruncateBeforeLoad" file="/workspace/tests/unit/load/database_loader_test.cpp" line="721" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.846" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_BulkInsertErrorHandling" file="/workspace/tests/unit/load/database_loader_test.cpp" line="746" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_DestructorFlushesBuffers" file="/workspace/tests/unit/load/database_loader_test.cpp" line="773" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_ConcurrentBatchLoading" file="/workspace/tests/unit/load/database_loader_test.cpp" line="807" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_ConnectionLossDuringBatch" file="/workspace/tests/unit/load/database_loader_test.cpp" line="855" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_PartialBatchFailure" file="/workspace/tests/unit/load/database_loader_test.cpp" line="886" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
  </testsuite>
  <testsuite name="PostgreSQLLoaderTest" tests="1" failures="0" disabled="1" skipped="0" errors="0" time="0." timestamp="1970-01-01T00:00:00.000">
    <testcase name="DISABLED_UseCopyCommand" file="/workspace/tests/unit/load/database_loader_test.cpp" line="434" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="PostgreSQLLoaderTest" />
  </testsuite>
  <testsuite name="MySQLLoaderTest" tests="1" failures="0" disabled="1" skipped="0" errors="0" time="0." timestamp="1970-01-01T00:00:00.000">
    <testcase name="DISABLED_MultiRowInsert" file="/workspace/tests/unit/load/database_loader_test.cpp" line="481" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MySQLLoaderTest" />
  </testsuite>
  <testsuite name="OmopDatabaseLoaderTest" tests="1" failures="0" disabled="1" skipped="0" errors="0" time="0." timestamp="1970-01-01T00:00:00.000">
    <testcase name="DISABLED_RecordConversionAndValidation" file="/workspace/tests/unit/load/database_loader_test.cpp" line="576" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="OmopDatabaseLoaderTest" />
  </testsuite>
  <testsuite name="ParallelDatabaseLoaderTest" tests="2" failures="0" disabled="1" skipped="0" errors="0" time="0." timestamp="2025-08-02T16:50:26.846">
    <testcase name="Initialization" file="/workspace/tests/unit/load/database_loader_test.cpp" line="642" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.846" classname="ParallelDatabaseLoaderTest" />
    <testcase name="DISABLED_ParallelBatchLoading" file="/workspace/tests/unit/load/database_loader_test.cpp" line="654" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="ParallelDatabaseLoaderTest" />
  </testsuite>
  <testsuite name="LoaderFactoryTest" tests="5" failures="0" disabled="0" skipped="0" errors="0" time="0." timestamp="2025-08-02T16:50:26.847">
    <testcase name="CreateDatabaseLoader" file="/workspace/tests/unit/load/database_loader_test.cpp" line="677" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.847" classname="LoaderFactoryTest" />
    <testcase name="CreatePostgreSQLLoader" file="/workspace/tests/unit/load/database_loader_test.cpp" line="686" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.847" classname="LoaderFactoryTest" />
    <testcase name="CreateMySQLLoader" file="/workspace/tests/unit/load/database_loader_test.cpp" line="695" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.847" classname="LoaderFactoryTest" />
    <testcase name="CreateOmopDatabaseLoader" file="/workspace/tests/unit/load/database_loader_test.cpp" line="704" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.847" classname="LoaderFactoryTest" />
    <testcase name="UnknownLoaderTypeThrows" file="/workspace/tests/unit/load/database_loader_test.cpp" line="713" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.847" classname="LoaderFactoryTest" />
  </testsuite>
  <testsuite name="LoaderBaseTest" tests="13" failures="0" disabled="0" skipped="0" errors="0" time="0.121" timestamp="2025-08-02T16:50:26.847">
    <testcase name="ConstructorInitializesName" file="/workspace/tests/unit/load/loader_base_test.cpp" line="159" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.847" classname="LoaderBaseTest" />
    <testcase name="InitializeSuccess" file="/workspace/tests/unit/load/loader_base_test.cpp" line="165" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.847" classname="LoaderBaseTest" />
    <testcase name="InitializeTwiceThrows" file="/workspace/tests/unit/load/loader_base_test.cpp" line="177" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.848" classname="LoaderBaseTest" />
    <testcase name="InitializeFailure" file="/workspace/tests/unit/load/loader_base_test.cpp" line="188" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.848" classname="LoaderBaseTest" />
    <testcase name="ProgressTracking" file="/workspace/tests/unit/load/loader_base_test.cpp" line="199" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.849" classname="LoaderBaseTest" />
    <testcase name="ErrorRecording" file="/workspace/tests/unit/load/loader_base_test.cpp" line="214" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.849" classname="LoaderBaseTest" />
    <testcase name="ElapsedTimeCalculation" file="/workspace/tests/unit/load/loader_base_test.cpp" line="243" status="run" result="completed" time="0.105" timestamp="2025-08-02T16:50:26.849" classname="LoaderBaseTest" />
    <testcase name="ConfigurationHelpers" file="/workspace/tests/unit/load/loader_base_test.cpp" line="255" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.954" classname="LoaderBaseTest" />
    <testcase name="GetStatisticsWithAdditional" file="/workspace/tests/unit/load/loader_base_test.cpp" line="277" status="run" result="completed" time="0.004" timestamp="2025-08-02T16:50:26.954" classname="LoaderBaseTest" />
    <testcase name="Finalization" file="/workspace/tests/unit/load/loader_base_test.cpp" line="294" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.959" classname="LoaderBaseTest" />
    <testcase name="ErrorLimitEnforced" file="/workspace/tests/unit/load/loader_base_test.cpp" line="551" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.959" classname="LoaderBaseTest" />
    <testcase name="ConcurrentAccess" file="/workspace/tests/unit/load/loader_base_test.cpp" line="641" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:26.960" classname="LoaderBaseTest" />
    <testcase name="ConcurrentErrorRecording" file="/workspace/tests/unit/load/loader_base_test.cpp" line="670" status="run" result="completed" time="0.007" timestamp="2025-08-02T16:50:26.961" classname="LoaderBaseTest" />
  </testsuite>
  <testsuite name="FileLoaderBaseTest" tests="9" failures="0" disabled="0" skipped="0" errors="0" time="0.11" timestamp="2025-08-02T16:50:26.968">
    <testcase name="InitializeWithExplicitPath" file="/workspace/tests/unit/load/loader_base_test.cpp" line="327" status="run" result="completed" time="0.003" timestamp="2025-08-02T16:50:26.968" classname="FileLoaderBaseTest" />
    <testcase name="InitializeWithAutoPath" file="/workspace/tests/unit/load/loader_base_test.cpp" line="338" status="run" result="completed" time="0.002" timestamp="2025-08-02T16:50:26.972" classname="FileLoaderBaseTest" />
    <testcase name="AppendMode" file="/workspace/tests/unit/load/loader_base_test.cpp" line="352" status="run" result="completed" time="0.072" timestamp="2025-08-02T16:50:26.975" classname="FileLoaderBaseTest" />
    <testcase name="FileOperations" file="/workspace/tests/unit/load/loader_base_test.cpp" line="379" status="run" result="completed" time="0.003" timestamp="2025-08-02T16:50:27.047" classname="FileLoaderBaseTest" />
    <testcase name="OperationsOnClosedFileThrow" file="/workspace/tests/unit/load/loader_base_test.cpp" line="402" status="run" result="completed" time="0.004" timestamp="2025-08-02T16:50:27.050" classname="FileLoaderBaseTest" />
    <testcase name="OpenAlreadyOpenFileThrows" file="/workspace/tests/unit/load/loader_base_test.cpp" line="411" status="run" result="completed" time="0.002" timestamp="2025-08-02T16:50:27.055" classname="FileLoaderBaseTest" />
    <testcase name="DestructorHandlesExceptions" file="/workspace/tests/unit/load/loader_base_test.cpp" line="588" status="run" result="completed" time="0.004" timestamp="2025-08-02T16:50:27.057" classname="FileLoaderBaseTest" />
    <testcase name="AdditionalStatistics" file="/workspace/tests/unit/load/loader_base_test.cpp" line="601" status="run" result="completed" time="0.003" timestamp="2025-08-02T16:50:27.062" classname="FileLoaderBaseTest" />
    <testcase name="ConcurrentWrites" file="/workspace/tests/unit/load/loader_base_test.cpp" line="722" status="run" result="completed" time="0.013" timestamp="2025-08-02T16:50:27.066" classname="FileLoaderBaseTest" />
  </testsuite>
  <testsuite name="NetworkLoaderBaseTest" tests="8" failures="0" disabled="0" skipped="0" errors="0" time="0.012" timestamp="2025-08-02T16:50:27.079">
    <testcase name="InitializeSuccess" file="/workspace/tests/unit/load/loader_base_test.cpp" line="437" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:27.079" classname="NetworkLoaderBaseTest" />
    <testcase name="InitializeWithoutEndpointThrows" file="/workspace/tests/unit/load/loader_base_test.cpp" line="451" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:27.080" classname="NetworkLoaderBaseTest" />
    <testcase name="InitializeWithCustomTimeout" file="/workspace/tests/unit/load/loader_base_test.cpp" line="458" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:27.080" classname="NetworkLoaderBaseTest" />
    <testcase name="NetworkStatisticsTracking" file="/workspace/tests/unit/load/loader_base_test.cpp" line="470" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:27.080" classname="NetworkLoaderBaseTest" />
    <testcase name="LoadOperation" file="/workspace/tests/unit/load/loader_base_test.cpp" line="492" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:27.080" classname="NetworkLoaderBaseTest" />
    <testcase name="BatchLoadOperation" file="/workspace/tests/unit/load/loader_base_test.cpp" line="512" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:27.081" classname="NetworkLoaderBaseTest" />
    <testcase name="FinalizationDisconnects" file="/workspace/tests/unit/load/loader_base_test.cpp" line="539" status="run" result="completed" time="0." timestamp="2025-08-02T16:50:27.082" classname="NetworkLoaderBaseTest" />
    <testcase name="AdditionalStatisticsWithConnectionDuration" file="/workspace/tests/unit/load/loader_base_test.cpp" line="616" status="run" result="completed" time="0.01" timestamp="2025-08-02T16:50:27.082" classname="NetworkLoaderBaseTest" />
  </testsuite>
  <testsuite name="MmapBatchLoaderSimpleTest" tests="2" failures="0" disabled="0" skipped="0" errors="0" time="0.107" timestamp="2025-08-02T16:50:27.092">
    <testcase name="ConstructorTest" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="75" status="run" result="completed" time="0.104" timestamp="2025-08-02T16:50:27.092" classname="MmapBatchLoaderSimpleTest" />
    <testcase name="TempDirectoryTest" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="91" status="run" result="completed" time="0.003" timestamp="2025-08-02T16:50:27.197" classname="MmapBatchLoaderSimpleTest" />
  </testsuite>
  <testsuite name="MmapSimpleTest" tests="1" failures="0" disabled="0" skipped="0" errors="0" time="0.104" timestamp="2025-08-02T16:50:27.200">
    <testcase name="BasicCreation" file="/workspace/tests/unit/load/mmap_simple_test.cpp" line="9" status="run" result="completed" time="0.104" timestamp="2025-08-02T16:50:27.200" classname="MmapSimpleTest" />
  </testsuite>
</testsuites>
