/**
 * @file test_complex_transformations_integration.cpp
 * @brief Integration tests for complex transformation scenarios
 */

#include <gtest/gtest.h>
#include "test_helpers/transform_test_fixture.h"
#include "test_helpers/test_data_generator.h"
#include "test_helpers/uk_test_utilities.h"
#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "transform/date_transformations.h"
#include "transform/numeric_transformations.h"
#include "transform/string_transformations.h"
#include "transform/conditional_transformations.h"
#include "transform/vocabulary_transformations.h"
#include "transform/custom_transformations.h"
#include "core/interfaces.h"
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#include "test_helpers/database_connection_factory.h"
#include <yaml-cpp/yaml.h>

namespace omop::test {

class ComplexTransformationIntegrationTest : public TransformTestFixture {
protected:
    void SetUp() override {
        TransformTestFixture::SetUp();
        
        // Register all transformation types
        register_all_transformations();
        
        // Create transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();
        
        // Initialize UK data generator for NHS-specific tests
        uk_generator_ = std::make_unique<uk::UKTestDataGenerator>();
    }
    
    void register_all_transformations() {
        auto& registry = transform::TransformationRegistry::instance();
        
        // Register date transformations
        registry.register_transformation("date_calculation", []() {
            return std::make_unique<transform::DateCalculationTransformation>();
        });
        
        registry.register_transformation("date_range_validation", []() {
            return std::make_unique<transform::DateRangeValidationTransformation>();
        });
        
        // Register numeric transformations
        registry.register_transformation("advanced_numeric_transform", []() {
            return std::make_unique<transform::AdvancedNumericTransformation>();
        });
        
        registry.register_transformation("numeric_validation", []() {
            return std::make_unique<transform::NumericValidationTransformation>();
        });
        
        // Register string transformations
        registry.register_transformation("string_manipulation", []() {
            return std::make_unique<transform::StringManipulationTransformation>();
        });
        
        registry.register_transformation("string_pattern_extraction", []() {
            return std::make_unique<transform::StringPatternExtractionTransformation>();
        });
        
        // Register conditional transformations
        registry.register_transformation("advanced_conditional", []() {
            return std::make_unique<transform::AdvancedConditionalTransformation>();
        });
        
        registry.register_transformation("lookup_table", []() {
            return std::make_unique<transform::LookupTableTransformation>();
        });
        
        // Register vocabulary transformations
        registry.register_transformation("concept_hierarchy", []() {
            return std::make_unique<transform::ConceptHierarchyTransformation>();
        });
        
        registry.register_transformation("domain_mapping", []() {
            return std::make_unique<transform::DomainMappingTransformation>();
        });
        
        registry.register_transformation("concept_relationship", []() {
            return std::make_unique<transform::ConceptRelationshipTransformation>();
        });
        
        // Register custom transformations
        registry.register_transformation("composite", []() {
            return std::make_unique<transform::CompositeTransformation>();
        });
        
        registry.register_transformation("age_group", []() {
            return std::make_unique<transform::DirectTransformation>();
        });
        
        registry.register_transformation("bmi_calculation", []() {
            return std::make_unique<transform::DirectTransformation>();
        });
        
        registry.register_transformation("medical_code_standardization", []() {
            return std::make_unique<transform::DirectTransformation>();
        });
        
        // Track for cleanup
        registered_transformations_ = {
            "date_calculation", "date_range_validation",
            "advanced_numeric_transform", "numeric_validation",
            "string_manipulation", "string_pattern_extraction",
            "advanced_conditional", "lookup_table",
            "concept_hierarchy", "domain_mapping", "concept_relationship",
            "composite", "age_group", "bmi_calculation", "medical_code_standardization"
        };
    }
    
    std::unique_ptr<transform::TransformationEngine> engine_;
    std::unique_ptr<uk::UKTestDataGenerator> uk_generator_;
    YAML::Node test_config_;
    
    // Helper functions for tests
    std::unique_ptr<extract::IDatabaseConnection> create_omop_db_connection() {
        // Use the centralized database connection factory
        try {
            return DatabaseConnectionFactory::createOmopConnection();
        } catch (const std::exception& e) {
            // Log warning but continue - some tests may not need database
            auto logger = common::Logger::get("complex-transform-test");
            logger->warn("Exception connecting to OMOP database: {}", e.what());
            return nullptr;
        }
    }
    
    std::unique_ptr<transform::VocabularyService> create_vocabulary_service() {
        // Create real vocabulary service with OMOP database connection
        auto omop_connection = create_omop_db_connection();
        if (!omop_connection) {
            return nullptr;
        }
        auto vocab_service = std::make_unique<transform::VocabularyService>(std::move(omop_connection));
        vocab_service->initialize(1000);
        return vocab_service;
    }
    
    std::unique_ptr<uk::UKTestDataGenerator> create_uk_test_generator() {
        return std::make_unique<uk::UKTestDataGenerator>();
    }
    
    void create_test_configuration(const std::string& yaml_config) {
        // Parse and configure the transformation engine with the YAML
        YAML::Node config = YAML::Load(yaml_config);
        
        // Store parsed configuration for use in tests
        test_config_ = config;
    }
    
    void initialize_engine_with_test_mode(const std::string& table_name) {
        core::ProcessingContext context;
        
        // Get the table mapping from the parsed YAML configuration
        if (!test_config_["tables"] || !test_config_["tables"][table_name]) {
            throw std::runtime_error("Table configuration not found for: " + table_name);
        }
        
        YAML::Node table_config = test_config_["tables"][table_name];
        
        std::unordered_map<std::string, std::any> config{
            {"table_name", table_name},
            {"test_mode", true},  // Enable test mode to bypass configuration manager
            {"table_mapping", table_config}  // Pass the actual table mapping configuration
        };
        engine_->initialize(config, context);
    }
    
    core::Record create_test_record(const std::unordered_map<std::string, std::any>& fields) {
        core::Record record;
        for (const auto& [key, value] : fields) {
            record.setField(key, value);
        }
        return record;
    }
};

// Test complex date calculations including age calculation and date arithmetic
TEST_F(ComplexTransformationIntegrationTest, DateCalculationTransformations) {
    std::string config_yaml = R"(
        tables:
          observation_period:
            source_table: patient_enrollment
            target_table: observation_period
            transformations:
              - source_column: birth_date
                target_column: age_at_enrollment
                type: date_calculation
                parameters:
                  operation: Age
                  reference_date_field: enrollment_date
                  output_format: "integer"
              - source_column: enrollment_date
                target_column: observation_period_start_date
                type: date_transform
                parameters:
                  input_format: "%Y-%m-%d"
                  output_format: "%Y-%m-%d"
              - source_column: enrollment_date
                target_column: observation_period_end_date
                type: date_calculation
                parameters:
                  operation: AddYears
                  offset_value: 1
                  output_format: "%Y-%m-%d"
              - source_column: last_visit_date
                target_column: days_since_last_visit
                type: date_calculation
                parameters:
                  operation: DateDiff
                  reference_date_field: current_date
                  diff_unit: days
    )";
    
    create_test_configuration(config_yaml);
    initialize_engine_with_test_mode("observation_period");
    
    core::ProcessingContext context;
    
    // Create test record
    auto record = create_test_record({
        {"patient_id", 12345},
        {"birth_date", std::string("1985-06-15")},
        {"enrollment_date", std::string("2023-01-15")},
        {"last_visit_date", std::string("2023-11-01")},
        {"current_date", std::string("2023-12-01")}
    });
    
    auto result = engine_->transform(record, context);
    ASSERT_TRUE(result.has_value());
    
    // Verify age calculation
    EXPECT_EQ(std::any_cast<int>(result->getField("age_at_enrollment")), 37);
    
    // Verify date addition
    EXPECT_EQ(std::any_cast<std::string>(result->getField("observation_period_end_date")), 
              "2024-01-15");
    
    // Verify date difference
    EXPECT_EQ(std::any_cast<int>(result->getField("days_since_last_visit")), 30);
}

// Test advanced numeric transformations including unit conversions and calculations
TEST_F(ComplexTransformationIntegrationTest, AdvancedNumericTransformations) {
    std::string config_yaml = R"(
        tables:
          lab_results:
            source_table: raw_labs
            target_table: measurement
            transformations:
              - source_column: glucose_mgdl
                target_column: glucose_mmol
                type: advanced_numeric_transform
                parameters:
                  operation: UnitConversion
                  from_unit: mg/dL
                  to_unit: mmol/L
                  round_to_decimal: 2
              - source_column: temperature_f
                target_column: temperature_c
                type: advanced_numeric_transform
                parameters:
                  operation: UnitConversion
                  from_unit: fahrenheit
                  to_unit: celsius
                  round_to_decimal: 1
              - source_column: weight_lbs
                target_column: weight_kg
                type: advanced_numeric_transform
                parameters:
                  operation: UnitConversion
                  from_unit: lb
                  to_unit: kg
                  round_to_decimal: 1
              - source_column: height_inches
                target_column: height_cm
                type: advanced_numeric_transform
                parameters:
                  operation: UnitConversion
                  from_unit: in
                  to_unit: cm
                  round_to_decimal: 1
    )";
    
    create_test_configuration(config_yaml);
    initialize_engine_with_test_mode("lab_results");
    
    core::ProcessingContext context;
    
    // Create test record with US units
    auto record = create_test_record({
        {"patient_id", 12345},
        {"glucose_mgdl", 144.0}, // 8.0 mmol/L
        {"temperature_f", 98.6}, // 37.0°C
        {"weight_lbs", 154.0}, // 70.0 kg
        {"height_inches", 70.0} // 177.8 cm
    });
    
    auto result = engine_->transform(record, context);
    ASSERT_TRUE(result.has_value());
    
    // Verify unit conversions
    EXPECT_NEAR(std::any_cast<double>(result->getField("glucose_mmol")), 8.0, 0.01);
    EXPECT_NEAR(std::any_cast<double>(result->getField("temperature_c")), 37.0, 0.1);
    EXPECT_NEAR(std::any_cast<double>(result->getField("weight_kg")), 70.0, 0.1);
    EXPECT_NEAR(std::any_cast<double>(result->getField("height_cm")), 177.8, 0.1);
}

// Test UK-specific medical data validation using UnifiedStringTransformation
TEST_F(ComplexTransformationIntegrationTest, UKMedicalDataValidation) {
    std::string config_yaml = R"(
        tables:
          uk_patient_data:
            source_table: raw_uk_patients
            target_table: person
            transformations:
              - source_column: nhs_number
                target_column: validated_nhs
                type: string_transform
                parameters:
                  operation: validate
                  validation_type: nhs_number
                  return_formatted: true
              - source_column: postcode
                target_column: formatted_postcode
                type: string_transform
                parameters:
                  operation: validate
                  validation_type: uk_postcode
                  return_formatted: true
              - source_column: phone
                target_column: validated_phone
                type: string_transform
                parameters:
                  operation: validate
                  validation_type: uk_phone
                  return_formatted: true
    )";
    
    create_test_configuration(config_yaml);
    initialize_engine_with_test_mode("uk_patient_data");
    
    core::ProcessingContext context;
    
    // Create test record with UK-specific data
    auto record = create_test_record({
        {"patient_id", 12345},
        {"nhs_number", std::string("**********")},  // Valid NHS number
        {"postcode", std::string("W1A0AX")},        // Valid UK postcode (no space)
        {"phone", std::string("07987654321")}       // Valid UK mobile
    });
    
    auto result = engine_->transform(record, context);
    ASSERT_TRUE(result.has_value());
    
    // Verify UK medical data validation and formatting
    EXPECT_EQ(std::any_cast<std::string>(result->getField("validated_nhs")), "**********");
    EXPECT_EQ(std::any_cast<std::string>(result->getField("formatted_postcode")), "W1A 0AX"); // Should add space
    EXPECT_EQ(std::any_cast<std::string>(result->getField("validated_phone")), "+447987654321"); // UK format
}

// Test complex string manipulation including parsing and formatting operations
TEST_F(ComplexTransformationIntegrationTest, StringManipulationTransformations) {
    std::string config_yaml = R"(
        tables:
          patient_data:
            source_table: raw_patients
            target_table: person
            transformations:
              - source_column: full_name
                target_column: first_name
                type: string_manipulation
                parameters:
                  operation: ExtractFirstWord
              - source_column: full_name
                target_column: last_name
                type: string_manipulation
                parameters:
                  operation: ExtractLastWord
              - source_column: address
                target_column: postcode
                type: string_pattern_extraction
                parameters:
                  pattern: "[A-Z]{1,2}[0-9][A-Z0-9]? ?[0-9][A-Z]{2}"
                  extract_group: 0
              - source_column: phone
                target_column: formatted_phone
                type: string_manipulation
                parameters:
                  operation: FormatPhoneNumber
                  country_code: "GB"
                  format: "+44 {area} {number}"
    )";
    
    create_test_configuration(config_yaml);
    initialize_engine_with_test_mode("patient_data");
    
    core::ProcessingContext context;
    
    // Create test record
    auto record = create_test_record({
        {"patient_id", 12345},
        {"full_name", std::string("John Smith")},
        {"address", std::string("123 Main Street, London SW1A 1AA, UK")},
        {"phone", std::string("02079460958")}
    });
    
    auto result = engine_->transform(record, context);
    ASSERT_TRUE(result.has_value());
    
    // Verify string manipulations
    EXPECT_EQ(std::any_cast<std::string>(result->getField("first_name")), "John");
    EXPECT_EQ(std::any_cast<std::string>(result->getField("last_name")), "Smith");
    EXPECT_EQ(std::any_cast<std::string>(result->getField("postcode")), "SW1A 1AA");
    EXPECT_EQ(std::any_cast<std::string>(result->getField("formatted_phone")), "+44 20 7946 0958");
}

// Test conditional transformations with complex rule-based logic
TEST_F(ComplexTransformationIntegrationTest, ConditionalTransformations) {
    std::string config_yaml = R"(
        tables:
          lab_results:
            source_table: raw_labs
            target_table: measurement
            transformations:
              - source_column: result_value
                target_column: result_category
                type: advanced_conditional
                parameters:
                  conditions:
                    - when: "value > 100"
                      then: "HIGH"
                    - when: "value < 50"
                      then: "LOW"
                    - default: "NORMAL"
              - source_column: test_code
                target_column: test_category
                type: lookup_table
                parameters:
                  lookup_table:
                    "GLUC": "Glucose"
                    "CHOL": "Cholesterol"
                    "BP": "Blood Pressure"
                    "TEMP": "Temperature"
                  default_value: "Unknown"
    )";
    
    create_test_configuration(config_yaml);
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("lab_results")}
    };
    engine_->initialize(config, context);
    
    // Test conditional categorization
    auto high_record = create_test_record({
        {"patient_id", 12345},
        {"result_value", 150.0},
        {"test_code", std::string("GLUC")}
    });
    
    auto result = engine_->transform(high_record, context);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(std::any_cast<std::string>(result->getField("result_category")), "HIGH");
    EXPECT_EQ(std::any_cast<std::string>(result->getField("test_category")), "Glucose");
    
    // Test lookup table
    auto unknown_record = create_test_record({
        {"patient_id", 12346},
        {"result_value", 75.0},
        {"test_code", std::string("UNKNOWN")}
    });
    
    result = engine_->transform(unknown_record, context);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(std::any_cast<std::string>(result->getField("test_category")), "Unknown");
}

// Test complex transformations involving vocabulary mapping and concept lookups
TEST_F(ComplexTransformationIntegrationTest, VocabularyTransformations) {
    // Initialize vocabulary service if not available
    if (!vocabulary_service_) {
        // Create a vocabulary service with real OMOP database connection
        vocabulary_service_ = create_vocabulary_service();
        ASSERT_NE(vocabulary_service_, nullptr) << "Failed to create vocabulary service";
    }
    
    std::string config_yaml = R"(
        tables:
          conditions:
            source_table: raw_conditions
            target_table: condition_occurrence
            transformations:
              - source_column: icd10_code
                target_column: condition_concept_id
                type: vocabulary_mapping
                parameters:
                  source_vocabulary: "ICD10"
                  target_vocabulary: "SNOMED"
                  relationship_type: "Maps to"
              - source_column: condition_code
                target_column: condition_type_concept_id
                type: concept_hierarchy
                parameters:
                  hierarchy_type: "Condition Type"
                  level: 2
              - source_column: body_site
                target_column: anatomic_site_concept_id
                type: domain_mapping
                parameters:
                  source_domain: "Anatomy"
                  target_domain: "Condition"
    )";
    
    create_test_configuration(config_yaml);
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("conditions")}
    };
    engine_->initialize(config, context);
    
    // Create test record
    auto record = create_test_record({
        {"patient_id", 12345},
        {"icd10_code", std::string("I10")}, // Essential hypertension
        {"condition_code", std::string("DIAGNOSIS")},
        {"body_site", std::string("CARDIOVASCULAR")}
    });
    
    auto result = engine_->transform(record, context);
    ASSERT_TRUE(result.has_value());
    
    // Verify vocabulary mappings
    EXPECT_GT(std::any_cast<int>(result->getField("condition_concept_id")), 0);
}

// Test composite transformations that combine multiple transformation types
TEST_F(ComplexTransformationIntegrationTest, CompositeTransformations) {
    std::string config_yaml = R"yaml(
        tables:
          vital_signs:
            source_table: raw_vitals
            target_table: measurement
            transformations:
              - source_column: systolic
                target_column: systolic_bp
                type: numeric_validation
                parameters:
                  min_value: 70
                  max_value: 200
                  unit: "mmHg"
              - source_column: diastolic
                target_column: diastolic_bp
                type: numeric_validation
                parameters:
                  min_value: 40
                  max_value: 130
                  unit: "mmHg"
              - source_columns: ["systolic_bp", "diastolic_bp"]
                target_column: bp_category
                type: composite
                parameters:
                  operation: BloodPressureCategory
                  systolic_field: systolic_bp
                  diastolic_field: diastolic_bp
                  categories:
                    normal: "systolic < 120 && diastolic < 80"
                    elevated: "systolic >= 120 && systolic < 130 && diastolic < 80"
                    stage1: "(systolic >= 130 && systolic < 140) || (diastolic >= 80 && diastolic < 90)"
                    stage2: "systolic >= 140 || diastolic >= 90"
                    crisis: "systolic >= 180 || diastolic >= 120"
    )yaml";
    
    create_test_configuration(config_yaml);
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("vital_signs")}
    };
    engine_->initialize(config, context);
    
    // Test normal blood pressure
    auto normal_record = create_test_record({
        {"patient_id", 12345},
        {"systolic", 110},
        {"diastolic", 70}
    });
    
    auto result = engine_->transform(normal_record, context);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(std::any_cast<std::string>(result->getField("bp_category")), "normal");
    
    // Test elevated blood pressure
    auto elevated_record = create_test_record({
        {"patient_id", 12346},
        {"systolic", 125},
        {"diastolic", 75}
    });
    
    result = engine_->transform(elevated_record, context);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(std::any_cast<std::string>(result->getField("bp_category")), "elevated");
}

// Test error handling and recovery mechanisms in complex transformation scenarios
TEST_F(ComplexTransformationIntegrationTest, ErrorHandlingAndRecovery) {
    std::string config_yaml = R"(
        tables:
          lab_results:
            source_table: raw_labs
            target_table: measurement
            transformations:
              - source_column: result_value
                target_column: validated_value
                type: numeric_validation
                parameters:
                  min_value: 0
                  max_value: 1000
                  on_error: "use_default"
                  default_value: 0
              - source_column: test_date
                target_column: measurement_date
                type: date_transform
                parameters:
                  input_format: "%Y-%m-%d"
                  output_format: "%Y-%m-%d"
                  on_error: "skip_field"
    )";
    
    create_test_configuration(config_yaml);
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("lab_results")}
    };
    engine_->initialize(config, context);
    
    // Test with invalid numeric value
    auto invalid_numeric = create_test_record({
        {"patient_id", 12345},
        {"result_value", 1500.0}, // Above max
        {"test_date", std::string("2023-01-15")}
    });
    
    auto result = engine_->transform(invalid_numeric, context);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(std::any_cast<double>(result->getField("validated_value")), 0.0); // Default value
    
    // Test with invalid date format
    auto invalid_date = create_test_record({
        {"patient_id", 12346},
        {"result_value", 100.0},
        {"test_date", std::string("15/01/2023")} // Wrong format
    });
    
    result = engine_->transform(invalid_date, context);
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->hasField("measurement_date")); // Field should be skipped
}

// Test performance optimization features including batch processing and caching
TEST_F(ComplexTransformationIntegrationTest, PerformanceOptimization) {
    std::string config_yaml = R"(
        tables:
          large_dataset:
            source_table: raw_large_data
            target_table: measurement
            transformations:
              - source_column: value
                target_column: normalized_value
                type: advanced_numeric_transform
                parameters:
                  operation: Normalize
                  method: "z_score"
                  batch_size: 1000
              - source_column: category
                target_column: category_id
                type: lookup_table
                parameters:
                  cache_size: 10000
                  lookup_table:
                    "A": 1
                    "B": 2
                    "C": 3
    )";
    
    create_test_configuration(config_yaml);
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("large_dataset")}
    };
    engine_->initialize(config, context);
    
    // Generate large batch of test records
    TestDataGenerator generator;
    std::vector<int64_t> patient_ids;
    for (int i = 1; i <= 1000; ++i) {
        patient_ids.push_back(i);
    }
    auto records = generator.generate_measurement_records(10000, patient_ids);
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    core::RecordBatch input_batch;
    for (const auto& record : records) {
        input_batch.addRecord(record);
    }
    
    auto result_batch = engine_->transform_batch(input_batch, context);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_EQ(result_batch.size(), 10000) << "Should transform all records";
    EXPECT_LT(duration.count(), 10000) << "Should complete within 10 seconds";
    
    std::cout << "Transformed " << records.size() << " records in " 
              << duration.count() << "ms" << std::endl;
}

// Test UK-specific transformations including NHS numbers, postcodes, and local formats
TEST_F(ComplexTransformationIntegrationTest, UKSpecificTransformations) {
    // Initialize UK test utilities if not available
    if (!uk_generator_) {
        uk_generator_ = create_uk_test_generator();
        ASSERT_NE(uk_generator_, nullptr) << "Failed to create UK test generator";
    }
    
    std::string config_yaml = R"(
        tables:
          uk_patients:
            source_table: raw_uk_patients
            target_table: person
            transformations:
              - source_column: nhs_number
                target_column: nhs_number_clean
                type: string_manipulation
                parameters:
                  operation: RemoveSpaces
              - source_column: nhs_number_clean
                target_column: nhs_number_valid
                type: validation
                parameters:
                  validator: "nhs_checksum"
              - source_column: postcode
                target_column: postcode_standard
                type: string_manipulation
                parameters:
                  operation: StandardizePostcode
                  country: "GB"
              - source_column: birth_date
                target_column: birth_datetime
                type: date_transform
                parameters:
                  input_format: "%d/%m/%Y"
                  output_format: "%Y-%m-%d"
                  timezone: "Europe/London"
    )";
    
    create_test_configuration(config_yaml);
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("uk_patients")}
    };
    engine_->initialize(config, context);
    
    // Create test record with UK-specific data
    auto record = create_test_record({
        {"patient_id", 12345},
        {"nhs_number", std::string("************")},
        {"postcode", std::string("sw1a 1aa")},
        {"birth_date", std::string("15/03/1980")}
    });
    
    auto result = engine_->transform(record, context);
    ASSERT_TRUE(result.has_value());
    
    // Verify UK-specific transformations
    EXPECT_EQ(std::any_cast<std::string>(result->getField("nhs_number_clean")), "**********");
    EXPECT_EQ(std::any_cast<std::string>(result->getField("postcode_standard")), "SW1A 1AA");
    EXPECT_EQ(std::any_cast<std::string>(result->getField("birth_datetime")), "1980-03-15");
}

} // namespace omop::test