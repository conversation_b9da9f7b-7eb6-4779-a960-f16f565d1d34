// tests/unit/transform/date_transformations_test.cpp

#include <gtest/gtest.h>
#include "transform/transformations.h"
#include "transform/date_transformations.h"
#include "core/interfaces.h"
#include <memory>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <yaml-cpp/yaml.h>

namespace omop::transform::test {

class DateTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        registry_ = &TransformationRegistry::instance();
    }

    void TearDown() override {
        context_.reset();
    }

    std::chrono::system_clock::time_point create_date(int year, int month, int day) {
        std::tm tm = {};
        tm.tm_year = year - 1900;
        tm.tm_mon = month - 1;
        tm.tm_mday = day;
        return std::chrono::system_clock::from_time_t(std::mktime(&tm));
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
};

// Test date transformation with string input using ISO format
TEST_F(DateTransformationsTest, DateTransformationStringInput) {
    auto transform = registry_->create_transformation("date_transform");

    YAML::Node params;
    params["format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d %H:%M:%S";
    params["add_time"] = true;
    params["default_time"] = "12:00:00";
    transform->configure(params);

    std::string input = "2024-03-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-03-15 12:00:00", std::any_cast<std::string>(result.value));
}

// Test UK date format transformation (DD/MM/YYYY to ISO format)
TEST_F(DateTransformationsTest, UKDateFormatTransformation) {
    auto transform = registry_->create_transformation("date_transform");

    YAML::Node params;
    params["format"] = "%d/%m/%Y";  // UK input format
    params["output_format"] = "%Y-%m-%d";  // ISO output format
    transform->configure(params);

    // Test various UK date formats
    std::vector<std::pair<std::string, std::string>> uk_test_cases = {
        {"25/12/2023", "2023-12-25"},  // Christmas Day
        {"29/02/2024", "2024-02-29"},  // Leap year
        {"01/04/2024", "2024-04-01"},  // Tax year start in UK
        {"31/10/2023", "2023-10-31"}   // Halloween
    };

    for (const auto& [uk_date, expected_iso] : uk_test_cases) {
        auto result = transform->transform_safe(uk_date, *context_);
        ASSERT_TRUE(result.is_success()) << "Failed to transform UK date: " << uk_date;
        EXPECT_EQ(expected_iso, std::any_cast<std::string>(result.value))
            << "UK date transformation mismatch for: " << uk_date;
    }
}

// Test date transformation with time_point input
TEST_F(DateTransformationsTest, DateTransformationTimePointInput) {
    auto transform = registry_->create_transformation("date_transform");

    YAML::Node params;
    params["output_format"] = "%Y-%m-%d";
    transform->configure(params);

    auto now = std::chrono::system_clock::now();
    auto result = transform->transform_safe(now, *context_);

    ASSERT_TRUE(result.is_success());
    std::string output = std::any_cast<std::string>(result.value);
    EXPECT_EQ(10, output.length()); // YYYY-MM-DD format
}

// Test date transformation with invalid input
TEST_F(DateTransformationsTest, DateTransformationInvalidInput) {
    auto transform = registry_->create_transformation("date_transform");

    YAML::Node params;
    params["format"] = "%Y-%m-%d";
    transform->configure(params);

    std::string input = "invalid-date";
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test date transformation validation
TEST_F(DateTransformationsTest, DateTransformationValidation) {
    auto transform = registry_->create_transformation("date_transform");

    EXPECT_TRUE(transform->validate_input(std::string("2024-03-15")));
    EXPECT_TRUE(transform->validate_input(std::chrono::system_clock::now()));
    EXPECT_FALSE(transform->validate_input(std::any{}));
    EXPECT_FALSE(transform->validate_input(123));
}

// Test date transformation type
TEST_F(DateTransformationsTest, DateTransformationType) {
    auto transform = registry_->create_transformation("date_transform");
    EXPECT_EQ("date_transform", transform->get_type());
}

// Test date calculation age
TEST_F(DateTransformationsTest, DateCalculationAge) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "age";
    transform->configure(params);

    // Input should be the birth date
    std::string input = "1990-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    // Age will be calculated from birth date to current date
    int age = std::any_cast<int>(result.value);
    EXPECT_GT(age, 30); // Should be around 34 in 2024
}

// Test date calculation age before birthday
TEST_F(DateTransformationsTest, DateCalculationAgeBeforeBirthday) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "age";
    transform->configure(params);

    // Input should be the birth date
    std::string input = "1990-12-31";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    // Age will be calculated from birth date to current date
    int age = std::any_cast<int>(result.value);
    EXPECT_GT(age, 30); // Should be around 33 in 2024
}

// Test date calculation date diff days
TEST_F(DateTransformationsTest, DateCalculationDateDiffDays) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "date_diff";
    params["unit"] = "days";
    params["reference_date"] = "2024-01-10";
    transform->configure(params);

    // Input date, reference date is 2024-01-10, so 2024-01-01 should give 9 days
    std::string input = "2024-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ(9, std::any_cast<int>(result.value));
}

// Test date calculation date diff months
TEST_F(DateTransformationsTest, DateCalculationDateDiffMonths) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "date_diff";
    params["unit"] = "months";
    params["reference_date"] = "2024-03-15";
    transform->configure(params);

    // Input date, reference date is 2024-03-15, so 2024-01-01 should give 2 months
    std::string input = "2024-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ(2, std::any_cast<int>(result.value));
}

// Test date calculation add days
TEST_F(DateTransformationsTest, DateCalculationAddDays) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "add_days";
    params["offset"] = 7;
    transform->configure(params);

    std::string input = "2024-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-01-08", std::any_cast<std::string>(result.value));
}

// Test date calculation add months
TEST_F(DateTransformationsTest, DateCalculationAddMonths) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "add_months";
    params["offset"] = 2;
    transform->configure(params);

    std::string input = "2024-01-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-03-15", std::any_cast<std::string>(result.value));
}

// Test date calculation add years
TEST_F(DateTransformationsTest, DateCalculationAddYears) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "add_years";
    params["offset"] = 5;
    transform->configure(params);

    std::string input = "2024-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2029-01-01", std::any_cast<std::string>(result.value));
}

// Test date calculation start of month
TEST_F(DateTransformationsTest, DateCalculationStartOfMonth) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "start_of_month";
    transform->configure(params);

    std::string input = "2024-03-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-03-01", std::any_cast<std::string>(result.value));
}

// Test date calculation end of month
TEST_F(DateTransformationsTest, DateCalculationEndOfMonth) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "end_of_month";
    transform->configure(params);

    std::string input = "2024-02-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-02-29", std::any_cast<std::string>(result.value)); // Leap year
}

// Test date calculation start of year
TEST_F(DateTransformationsTest, DateCalculationStartOfYear) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "start_of_year";
    transform->configure(params);

    std::string input = "2024-06-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-01-01", std::any_cast<std::string>(result.value));
}

// Test date calculation end of year
TEST_F(DateTransformationsTest, DateCalculationEndOfYear) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "end_of_year";
    transform->configure(params);

    std::string input = "2024-06-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-12-31", std::any_cast<std::string>(result.value));
}

// Test date calculation time_point input
TEST_F(DateTransformationsTest, DateCalculationTimePointInput) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "add_days";
    params["offset"] = 1;
    transform->configure(params);

    auto now = std::chrono::system_clock::now();
    auto result = transform->transform_safe(now, *context_);

    ASSERT_TRUE(result.is_success());
    // Should return a date string
    std::string output = std::any_cast<std::string>(result.value);
    EXPECT_FALSE(output.empty());
}

// Test date range validation time_point input
TEST_F(DateTransformationsTest, DateRangeValidationTimePointInput) {
    auto transform = registry_->create_transformation("date_range_validation");

    YAML::Node params;
    params["min_date"] = "2020-01-01";
    params["max_date"] = "2030-12-31";
    transform->configure(params);

    auto now = std::chrono::system_clock::now();
    auto result = transform->transform_safe(now, *context_);

    ASSERT_TRUE(result.is_success());
    // Should return a formatted date string
    std::string output = std::any_cast<std::string>(result.value);
    EXPECT_FALSE(output.empty());
}

// Test date calculation reference now
TEST_F(DateTransformationsTest, DateCalculationReferenceNow) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "age";
    transform->configure(params);

    // Use a birth date from 1990
    std::string input = "1990-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    // Age should be greater than 30 (born in 1990, now in 2024+)
    EXPECT_GT(std::any_cast<int>(result.value), 30);
}

} // namespace omop::transform::test