// tests/unit/transform/transformation_utils_test.cpp

#include <gtest/gtest.h>
#include "transform/transformations.h"
#include <chrono>
#include <vector>
#include <string>

namespace omop::transform::test {

class TransformationUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup code if needed
    }

    void TearDown() override {
        // Cleanup code if needed
    }
};

// Test parsing dates with multiple format attempts
TEST_F(TransformationUtilsTest, ParseDateWithValidFormats) {
    std::vector<std::string> formats = {"%Y-%m-%d", "%Y/%m/%d", "%m/%d/%Y"};

    auto result = TransformationUtils::parse_date("2024-03-15", formats);
    ASSERT_TRUE(result.has_value());

    auto time_t = std::chrono::system_clock::to_time_t(*result);
    std::tm* tm = std::localtime(&time_t);
    EXPECT_EQ(2024, tm->tm_year + 1900);
    EXPECT_EQ(2, tm->tm_mon); // March is month 2 (0-indexed)
    EXPECT_EQ(15, tm->tm_mday);
}

// Test parsing date with invalid format
TEST_F(TransformationUtilsTest, ParseDateWithInvalidFormat) {
    std::vector<std::string> formats = {"%Y-%m-%d"};

    auto result = TransformationUtils::parse_date("invalid-date", formats);
    EXPECT_FALSE(result.has_value());
}

// Test parsing date with ISO 8601 fallback formats
TEST_F(TransformationUtilsTest, ParseDateWithISO8601Fallback) {
    std::vector<std::string> formats = {"invalid-format"};

    auto result = TransformationUtils::parse_date("2024-03-15T10:30:00", formats);
    ASSERT_TRUE(result.has_value());
}

// Test formatting date to string
TEST_F(TransformationUtilsTest, FormatDateToString) {
    auto now = std::chrono::system_clock::now();
    std::string formatted = TransformationUtils::format_date(now, "%Y-%m-%d");

    // Verify format pattern
    EXPECT_EQ(10, formatted.length());
    EXPECT_EQ('-', formatted[4]);
    EXPECT_EQ('-', formatted[7]);
}

// Test unit conversion for length units
TEST_F(TransformationUtilsTest, ConvertLengthUnits) {
    double result = TransformationUtils::convert_units(100.0, "cm", "m");
    EXPECT_DOUBLE_EQ(1.0, result);

    result = TransformationUtils::convert_units(1.0, "km", "m");
    EXPECT_DOUBLE_EQ(1000.0, result);

    result = TransformationUtils::convert_units(12.0, "in", "ft");
    EXPECT_DOUBLE_EQ(1.0, result);
}

// Test unit conversion for weight units
TEST_F(TransformationUtilsTest, ConvertWeightUnits) {
    double result = TransformationUtils::convert_units(1000.0, "g", "kg");
    EXPECT_DOUBLE_EQ(1.0, result);

    result = TransformationUtils::convert_units(1.0, "lb", "kg");
    EXPECT_NEAR(0.453592, result, 0.000001);
}

// Test unit conversion for temperature with proper offset calculations
TEST_F(TransformationUtilsTest, ConvertTemperatureUnits) {
    double result = TransformationUtils::convert_units(0.0, "c", "f");
    EXPECT_DOUBLE_EQ(32.0, result);

    result = TransformationUtils::convert_units(100.0, "c", "f");
    EXPECT_DOUBLE_EQ(212.0, result);

    result = TransformationUtils::convert_units(32.0, "f", "c");
    EXPECT_DOUBLE_EQ(0.0, result);

    result = TransformationUtils::convert_units(0.0, "c", "k");
    EXPECT_DOUBLE_EQ(273.15, result);
}

// Test unit conversion with same units
TEST_F(TransformationUtilsTest, ConvertSameUnits) {
    double result = TransformationUtils::convert_units(100.0, "m", "m");
    EXPECT_DOUBLE_EQ(100.0, result);
}

// Test unit conversion with unsupported units should throw exception
TEST_F(TransformationUtilsTest, ConvertUnsupportedUnits) {
    EXPECT_THROW(
        TransformationUtils::convert_units(100.0, "invalid", "m"),
        common::TransformationException
    );
}

// Test string normalization with case sensitivity
TEST_F(TransformationUtilsTest, NormalizeStringCaseSensitive) {
    std::string result = TransformationUtils::normalize_string("  Hello World  ", true, true);
    EXPECT_EQ("Hello World", result);
}

// Test string normalization without case sensitivity
TEST_F(TransformationUtilsTest, NormalizeStringCaseInsensitive) {
    std::string result = TransformationUtils::normalize_string("  Hello World  ", false, true);
    EXPECT_EQ("HELLO WORLD", result);
}

// Test string normalization without trimming
TEST_F(TransformationUtilsTest, NormalizeStringNoTrim) {
    std::string result = TransformationUtils::normalize_string("  Hello  ", true, false);
    EXPECT_EQ("  Hello  ", result);
}

// Test numeric range validation with valid values
TEST_F(TransformationUtilsTest, ValidateNumericRangeValid) {
    EXPECT_TRUE(TransformationUtils::validate_numeric_range(5.0, 0.0, 10.0));
    EXPECT_TRUE(TransformationUtils::validate_numeric_range(0.0, 0.0, 10.0));
    EXPECT_TRUE(TransformationUtils::validate_numeric_range(10.0, 0.0, 10.0));
}

// Test numeric range validation with invalid values
TEST_F(TransformationUtilsTest, ValidateNumericRangeInvalid) {
    EXPECT_FALSE(TransformationUtils::validate_numeric_range(-1.0, 0.0, 10.0));
    EXPECT_FALSE(TransformationUtils::validate_numeric_range(11.0, 0.0, 10.0));
}

// Test numeric range validation with optional bounds
TEST_F(TransformationUtilsTest, ValidateNumericRangeOptionalBounds) {
    EXPECT_TRUE(TransformationUtils::validate_numeric_range(100.0, std::nullopt, 200.0));
    EXPECT_TRUE(TransformationUtils::validate_numeric_range(-100.0, -200.0, std::nullopt));
}

// Test extract numeric from string with various formats
TEST_F(TransformationUtilsTest, ExtractNumericFromString) {
    EXPECT_DOUBLE_EQ(123.45, TransformationUtils::extract_numeric("123.45", 0.0));
    EXPECT_DOUBLE_EQ(-123.45, TransformationUtils::extract_numeric("-123.45", 0.0));
    EXPECT_DOUBLE_EQ(123.0, TransformationUtils::extract_numeric("abc123def", 0.0));
    EXPECT_DOUBLE_EQ(99.0, TransformationUtils::extract_numeric("no numbers here", 99.0));
}

// Test pattern matching with valid patterns
TEST_F(TransformationUtilsTest, MatchesPatternValid) {
    EXPECT_TRUE(TransformationUtils::matches_pattern("<EMAIL>", R"(^[\w\.-]+@[\w\.-]+\.\w+$)"));
    EXPECT_TRUE(TransformationUtils::matches_pattern("123-45-6789", R"(\d{3}-\d{2}-\d{4})"));
}

// Test pattern matching with invalid patterns
TEST_F(TransformationUtilsTest, MatchesPatternInvalid) {
    EXPECT_FALSE(TransformationUtils::matches_pattern("invalid-email", R"(^[\w\.-]+@[\w\.-]+\.\w+$)"));
}

// Test pattern matching with invalid regex should throw exception
TEST_F(TransformationUtilsTest, MatchesPatternInvalidRegex) {
    EXPECT_THROW(
        TransformationUtils::matches_pattern("test", "[invalid"),
        common::TransformationException
    );
}

// Test string splitting with comma delimiter
TEST_F(TransformationUtilsTest, SplitString) {
    auto parts = TransformationUtils::split_string("one,two,three", ',');
    ASSERT_EQ(3, parts.size());
    EXPECT_EQ("one", parts[0]);
    EXPECT_EQ("two", parts[1]);
    EXPECT_EQ("three", parts[2]);
}

// Test string splitting with empty parts
TEST_F(TransformationUtilsTest, SplitStringEmptyParts) {
    auto parts = TransformationUtils::split_string("one,,three", ',');
    ASSERT_EQ(3, parts.size());
    EXPECT_EQ("one", parts[0]);
    EXPECT_EQ("", parts[1]);
    EXPECT_EQ("three", parts[2]);
}

// Test string joining with delimiter
TEST_F(TransformationUtilsTest, JoinStrings) {
    std::vector<std::string> parts = {"one", "two", "three"};
    std::string result = TransformationUtils::join_strings(parts, ", ");
    EXPECT_EQ("one, two, three", result);
}

// Test string joining with empty vector
TEST_F(TransformationUtilsTest, JoinStringsEmpty) {
    std::vector<std::string> parts;
    std::string result = TransformationUtils::join_strings(parts, ", ");
    EXPECT_EQ("", result);
}

// Test age calculation with valid dates
TEST_F(TransformationUtilsTest, CalculateAge) {
    // Create birthdate and reference date
    std::tm birth_tm = {};
    birth_tm.tm_year = 90;  // 1990
    birth_tm.tm_mon = 0;    // January
    birth_tm.tm_mday = 15;
    auto birthdate = std::chrono::system_clock::from_time_t(std::mktime(&birth_tm));

    std::tm ref_tm = {};
    ref_tm.tm_year = 124;   // 2024
    ref_tm.tm_mon = 2;      // March
    ref_tm.tm_mday = 15;
    auto reference = std::chrono::system_clock::from_time_t(std::mktime(&ref_tm));

    int age = TransformationUtils::calculate_age(birthdate, reference);
    EXPECT_EQ(34, age);
}

// Test age calculation when birthday hasn't occurred
TEST_F(TransformationUtilsTest, CalculateAgeBeforeBirthday) {
    std::tm birth_tm = {};
    birth_tm.tm_year = 90;  // 1990
    birth_tm.tm_mon = 11;   // December
    birth_tm.tm_mday = 15;
    auto birthdate = std::chrono::system_clock::from_time_t(std::mktime(&birth_tm));

    std::tm ref_tm = {};
    ref_tm.tm_year = 124;   // 2024
    ref_tm.tm_mon = 2;      // March
    ref_tm.tm_mday = 15;
    auto reference = std::chrono::system_clock::from_time_t(std::mktime(&ref_tm));

    int age = TransformationUtils::calculate_age(birthdate, reference);
    EXPECT_EQ(33, age);
}

// Test date difference calculation in days
TEST_F(TransformationUtilsTest, CalculateDateDifferenceDays) {
    auto start = std::chrono::system_clock::now();
    auto end = start + std::chrono::days(10);

    int diff = TransformationUtils::calculate_date_difference(start, end, "days");
    EXPECT_EQ(10, diff);
}

// Test date difference calculation in hours
TEST_F(TransformationUtilsTest, CalculateDateDifferenceHours) {
    auto start = std::chrono::system_clock::now();
    auto end = start + std::chrono::hours(48);

    int diff = TransformationUtils::calculate_date_difference(start, end, "hours");
    EXPECT_EQ(48, diff);
}

// Test date difference calculation with invalid unit should default to days
TEST_F(TransformationUtilsTest, CalculateDateDifferenceInvalidUnit) {
    auto start = std::chrono::system_clock::now();
    auto end = start + std::chrono::days(5);

    int diff = TransformationUtils::calculate_date_difference(start, end, "invalid");
    EXPECT_EQ(5, diff);
}

// Test UK NHS number validation
TEST_F(TransformationUtilsTest, UKNHSNumberValidation) {
    // Valid NHS numbers (calculated using official Modulus 11 algorithm)
    // **********: 9×10+4×9+3×8+4×7+7×6+6×5+5×4+9×3+1×2 = 299, remainder=2, checksum=9
    EXPECT_TRUE(TransformationUtils::is_uk_nhs_number("**********"));
    // **********: 9×10+8×9+7×8+6×7+5×6+4×5+3×4+2×3+1×2 = 330, remainder=0, checksum=0
    EXPECT_TRUE(TransformationUtils::is_uk_nhs_number("**********"));
    
    // Invalid NHS numbers
    EXPECT_FALSE(TransformationUtils::is_uk_nhs_number(""));
    EXPECT_FALSE(TransformationUtils::is_uk_nhs_number("123456789")); // Too short
    EXPECT_FALSE(TransformationUtils::is_uk_nhs_number("12345678901")); // Too long
    EXPECT_FALSE(TransformationUtils::is_uk_nhs_number("123456789a")); // Contains letter
    EXPECT_FALSE(TransformationUtils::is_uk_nhs_number("**********")); // All zeros
    EXPECT_FALSE(TransformationUtils::is_uk_nhs_number("**********")); // All same digit
    EXPECT_FALSE(TransformationUtils::is_uk_nhs_number("**********")); // Starts with 0
}

// ===== CONCEPT HIERARCHY FUNCTION TESTS =====

// Test leaf concept identification with realistic OMOP concept IDs
TEST_F(TransformationUtilsTest, LeafConceptIdentification) {
    // Valid leaf concepts (realistic OMOP CDM IDs)
    EXPECT_TRUE(TransformationUtils::is_leaf_concept(40001001));  // Condition leaf (high ID in range)
    EXPECT_TRUE(TransformationUtils::is_leaf_concept(1500789));   // Drug leaf (in drug range with high mod 1000)
    EXPECT_TRUE(TransformationUtils::is_leaf_concept(2500789));   // Procedure leaf (in procedure range with high mod 1000)
    EXPECT_TRUE(TransformationUtils::is_leaf_concept(46000567));  // Measurement leaf (in measurement range)
    EXPECT_TRUE(TransformationUtils::is_leaf_concept(44500890));  // Observation leaf (in observation range)
    EXPECT_TRUE(TransformationUtils::is_leaf_concept(200500123)); // Condition leaf (in extended condition range)
    EXPECT_TRUE(TransformationUtils::is_leaf_concept(50000000));  // High concept ID (general heuristic)
    
    // Invalid leaf concepts
    EXPECT_FALSE(TransformationUtils::is_leaf_concept(0));    // Invalid concept ID
    EXPECT_FALSE(TransformationUtils::is_leaf_concept(-1));   // Negative concept ID
    EXPECT_FALSE(TransformationUtils::is_leaf_concept(19));   // Root concept (Condition domain)
    EXPECT_FALSE(TransformationUtils::is_leaf_concept(13));   // Root concept (Drug domain)
    EXPECT_FALSE(TransformationUtils::is_leaf_concept(100));  // Low concept ID (likely root)
    EXPECT_FALSE(TransformationUtils::is_leaf_concept(1000)); // Low concept ID with low mod value
}

// Test root concept identification with realistic OMOP concept IDs
TEST_F(TransformationUtilsTest, RootConceptIdentification) {
    // Valid root concepts (known OMOP CDM root concepts)
    EXPECT_TRUE(TransformationUtils::is_root_concept(19));        // Condition domain root
    EXPECT_TRUE(TransformationUtils::is_root_concept(13));        // Drug domain root
    EXPECT_TRUE(TransformationUtils::is_root_concept(10));        // Procedure domain root
    EXPECT_TRUE(TransformationUtils::is_root_concept(21));        // Measurement domain root
    EXPECT_TRUE(TransformationUtils::is_root_concept(27));        // Observation domain root
    EXPECT_TRUE(TransformationUtils::is_root_concept(17));        // Device domain root
    EXPECT_TRUE(TransformationUtils::is_root_concept(9));         // Visit domain root
    EXPECT_TRUE(TransformationUtils::is_root_concept(441840));    // Clinical finding (SNOMED root)
    EXPECT_TRUE(TransformationUtils::is_root_concept(404684));    // Clinical drug root
    EXPECT_TRUE(TransformationUtils::is_root_concept(71388));     // Procedure root
    EXPECT_TRUE(TransformationUtils::is_root_concept(100));       // Low concept ID (heuristic)
    EXPECT_TRUE(TransformationUtils::is_root_concept(500));       // Low concept ID (heuristic)
    EXPECT_TRUE(TransformationUtils::is_root_concept(900000000)); // Domain root pattern
    
    // Invalid root concepts
    EXPECT_FALSE(TransformationUtils::is_root_concept(0));        // Invalid concept ID
    EXPECT_FALSE(TransformationUtils::is_root_concept(-1));       // Negative concept ID
    EXPECT_FALSE(TransformationUtils::is_root_concept(40001001)); // High concept ID (likely leaf)
    EXPECT_FALSE(TransformationUtils::is_root_concept(50000000)); // Very high concept ID (likely leaf)
}

// Test ancestor concept relationship with realistic OMOP concept IDs
TEST_F(TransformationUtilsTest, AncestorConceptRelationship) {
    // Valid ancestor relationships (using realistic OMOP domain ranges)
    EXPECT_TRUE(TransformationUtils::is_ancestor_concept(19, 40001001));    // Condition domain root -> leaf
    EXPECT_TRUE(TransformationUtils::is_ancestor_concept(441840, 40001002)); // Clinical finding -> specific condition
    EXPECT_TRUE(TransformationUtils::is_ancestor_concept(13, 1500789));     // Drug domain root -> drug leaf
    EXPECT_TRUE(TransformationUtils::is_ancestor_concept(404684, 1500456)); // Clinical drug root -> specific drug
    EXPECT_TRUE(TransformationUtils::is_ancestor_concept(10, 2500789));     // Procedure domain root -> procedure leaf
    EXPECT_TRUE(TransformationUtils::is_ancestor_concept(21, 3500123));     // Measurement domain root -> measurement leaf
    EXPECT_TRUE(TransformationUtils::is_ancestor_concept(27, 4500456));     // Observation domain root -> observation leaf
    
    // Valid relationships within same domain (general heuristic)
    EXPECT_TRUE(TransformationUtils::is_ancestor_concept(1000000, 1500000)); // Drug domain: ancestor < descendant with gap
    EXPECT_TRUE(TransformationUtils::is_ancestor_concept(40000000, 42000000)); // Same domain with sufficient gap
    
    // Invalid ancestor relationships
    EXPECT_FALSE(TransformationUtils::is_ancestor_concept(0, 40001001));    // Invalid ancestor
    EXPECT_FALSE(TransformationUtils::is_ancestor_concept(19, 0));          // Invalid descendant
    EXPECT_FALSE(TransformationUtils::is_ancestor_concept(40001001, 19));   // Reversed relationship
    EXPECT_FALSE(TransformationUtils::is_ancestor_concept(19, 19));         // Same concept ID
    EXPECT_FALSE(TransformationUtils::is_ancestor_concept(19, 1500789));    // Different domains (Condition -> Drug)
    EXPECT_FALSE(TransformationUtils::is_ancestor_concept(1500000, 1000000)); // Reversed order in same domain
}

// Test descendant concept relationship with realistic OMOP concept IDs
TEST_F(TransformationUtilsTest, DescendantConceptRelationship) {
    // Valid descendant relationships (inverse of ancestor relationships)
    EXPECT_TRUE(TransformationUtils::is_descendant_concept(40001001, 19));    // Condition leaf -> domain root
    EXPECT_TRUE(TransformationUtils::is_descendant_concept(40001002, 441840)); // Specific condition -> clinical finding
    EXPECT_TRUE(TransformationUtils::is_descendant_concept(1500789, 13));     // Drug leaf -> domain root
    EXPECT_TRUE(TransformationUtils::is_descendant_concept(1500456, 404684)); // Specific drug -> clinical drug root
    EXPECT_TRUE(TransformationUtils::is_descendant_concept(2500789, 10));     // Procedure leaf -> domain root
    EXPECT_TRUE(TransformationUtils::is_descendant_concept(3500123, 21));     // Measurement leaf -> domain root
    EXPECT_TRUE(TransformationUtils::is_descendant_concept(4500456, 27));     // Observation leaf -> domain root
    
    // Valid relationships within same domain (general heuristic)
    EXPECT_TRUE(TransformationUtils::is_descendant_concept(1500000, 1000000)); // Drug domain: descendant > ancestor with gap
    EXPECT_TRUE(TransformationUtils::is_descendant_concept(42000000, 40000000)); // Same domain with sufficient gap
    
    // Invalid descendant relationships
    EXPECT_FALSE(TransformationUtils::is_descendant_concept(0, 19));          // Invalid descendant
    EXPECT_FALSE(TransformationUtils::is_descendant_concept(40001001, 0));    // Invalid ancestor
    EXPECT_FALSE(TransformationUtils::is_descendant_concept(19, 40001001));   // Reversed relationship
    EXPECT_FALSE(TransformationUtils::is_descendant_concept(19, 19));         // Same concept ID
    EXPECT_FALSE(TransformationUtils::is_descendant_concept(1500123, 19));    // Different domains (Drug -> Condition)
    EXPECT_FALSE(TransformationUtils::is_descendant_concept(1000000, 1500000)); // Reversed order in same domain
}

// ===== FIELD VALIDATION FUNCTION TESTS =====

// Test required field validation
TEST_F(TransformationUtilsTest, RequiredFieldValidation) {
    // Valid required fields
    EXPECT_TRUE(TransformationUtils::is_required_field_valid(std::any(123), true));
    EXPECT_TRUE(TransformationUtils::is_required_field_valid(std::any(std::string("valid")), true));
    EXPECT_TRUE(TransformationUtils::is_required_field_valid(std::any(3.14), true));
    
    // Invalid required fields
    EXPECT_FALSE(TransformationUtils::is_required_field_valid(std::any(), true));           // No value
    EXPECT_FALSE(TransformationUtils::is_required_field_valid(std::any(0), true));          // Zero ID
    EXPECT_FALSE(TransformationUtils::is_required_field_valid(std::any(-1), true));         // Negative ID
    EXPECT_FALSE(TransformationUtils::is_required_field_valid(std::any(std::string("")), true)); // Empty string
    EXPECT_FALSE(TransformationUtils::is_required_field_valid(std::any(std::string("null")), true)); // Null string
    EXPECT_FALSE(TransformationUtils::is_required_field_valid(std::any(std::string("NULL")), true)); // NULL string
    
    // Not required fields (always valid)
    EXPECT_TRUE(TransformationUtils::is_required_field_valid(std::any(), false));
    EXPECT_TRUE(TransformationUtils::is_required_field_valid(std::any(0), false));
    EXPECT_TRUE(TransformationUtils::is_required_field_valid(std::any(std::string("")), false));
}

// Test optional field validation
TEST_F(TransformationUtilsTest, OptionalFieldValidation) {
    // Valid optional fields (can be null or have valid values)
    EXPECT_TRUE(TransformationUtils::is_optional_field_valid(std::any()));                    // Null is valid
    EXPECT_TRUE(TransformationUtils::is_optional_field_valid(std::any(123)));                 // Valid value
    EXPECT_TRUE(TransformationUtils::is_optional_field_valid(std::any(std::string("valid")))); // Valid string
    
    // Invalid optional fields (if they have values, they should be valid)
    EXPECT_FALSE(TransformationUtils::is_optional_field_valid(std::any(0)));                  // Zero ID
    EXPECT_FALSE(TransformationUtils::is_optional_field_valid(std::any(std::string("null")))); // Null string
    EXPECT_FALSE(TransformationUtils::is_optional_field_valid(std::any(std::string(""))));    // Empty string
}

// Test mandatory field validation
TEST_F(TransformationUtilsTest, MandatoryFieldValidation) {
    // Valid mandatory fields
    EXPECT_TRUE(TransformationUtils::is_mandatory_field_valid(std::any(123)));
    EXPECT_TRUE(TransformationUtils::is_mandatory_field_valid(std::any(std::string("valid"))));
    EXPECT_TRUE(TransformationUtils::is_mandatory_field_valid(std::any(3.14)));
    
    // Invalid mandatory fields
    EXPECT_FALSE(TransformationUtils::is_mandatory_field_valid(std::any()));                    // No value
    EXPECT_FALSE(TransformationUtils::is_mandatory_field_valid(std::any(0)));                   // Zero ID
    EXPECT_FALSE(TransformationUtils::is_mandatory_field_valid(std::any(std::string(""))));     // Empty string
    EXPECT_FALSE(TransformationUtils::is_mandatory_field_valid(std::any(std::string("null")))); // Null string
    EXPECT_FALSE(TransformationUtils::is_mandatory_field_valid(std::any(std::string("NULL")))); // NULL string
    EXPECT_FALSE(TransformationUtils::is_mandatory_field_valid(std::any(std::string("undefined")))); // Undefined
    EXPECT_FALSE(TransformationUtils::is_mandatory_field_valid(std::any(std::string("N/A"))));  // N/A
}

// Test primary field validation
TEST_F(TransformationUtilsTest, PrimaryFieldValidation) {
    // Valid primary fields
    EXPECT_TRUE(TransformationUtils::is_primary_field_valid(std::any(123)));
    EXPECT_TRUE(TransformationUtils::is_primary_field_valid(std::any(std::string("valid_key"))));
    
    // Invalid primary fields
    EXPECT_FALSE(TransformationUtils::is_primary_field_valid(std::any()));                     // No value
    EXPECT_FALSE(TransformationUtils::is_primary_field_valid(std::any(0)));                    // Zero ID
    EXPECT_FALSE(TransformationUtils::is_primary_field_valid(std::any(-1)));                   // Negative ID
    EXPECT_FALSE(TransformationUtils::is_primary_field_valid(std::any(std::string(""))));      // Empty string
    EXPECT_FALSE(TransformationUtils::is_primary_field_valid(std::any(3.14)));                 // Float not allowed
    EXPECT_FALSE(TransformationUtils::is_primary_field_valid(std::any(true)));                 // Bool not allowed
}

// Test secondary field validation
TEST_F(TransformationUtilsTest, SecondaryFieldValidation) {
    // Valid secondary fields (can be null or valid references)
    EXPECT_TRUE(TransformationUtils::is_secondary_field_valid(std::any()));                    // Null is valid
    EXPECT_TRUE(TransformationUtils::is_secondary_field_valid(std::any(123)));                 // Valid reference
    
    // Invalid secondary fields
    EXPECT_FALSE(TransformationUtils::is_secondary_field_valid(std::any(0)));                  // Zero reference
    EXPECT_FALSE(TransformationUtils::is_secondary_field_valid(std::any(-1)));                 // Negative reference
    EXPECT_FALSE(TransformationUtils::is_secondary_field_valid(std::any(std::string("ref")))); // String not allowed
    EXPECT_FALSE(TransformationUtils::is_secondary_field_valid(std::any(3.14)));               // Float not allowed
}

// ===== STATUS FUNCTION TESTS =====

// Test active status identification
TEST_F(TransformationUtilsTest, ActiveStatusIdentification) {
    // Valid active status values
    EXPECT_TRUE(TransformationUtils::is_active_status(std::any(std::string("active"))));
    EXPECT_TRUE(TransformationUtils::is_active_status(std::any(std::string("A"))));
    EXPECT_TRUE(TransformationUtils::is_active_status(std::any(std::string("1"))));
    EXPECT_TRUE(TransformationUtils::is_active_status(std::any(std::string("true"))));
    EXPECT_TRUE(TransformationUtils::is_active_status(std::any(std::string("yes"))));
    EXPECT_TRUE(TransformationUtils::is_active_status(std::any(std::string("enabled"))));
    EXPECT_TRUE(TransformationUtils::is_active_status(std::any(1)));
    EXPECT_TRUE(TransformationUtils::is_active_status(std::any(true)));
    
    // Invalid active status values
    EXPECT_FALSE(TransformationUtils::is_active_status(std::any()));                           // No value
    EXPECT_FALSE(TransformationUtils::is_active_status(std::any(std::string("inactive"))));    // Inactive
    EXPECT_FALSE(TransformationUtils::is_active_status(std::any(std::string("0"))));           // Zero
    EXPECT_FALSE(TransformationUtils::is_active_status(std::any(std::string("false"))));       // False
    EXPECT_FALSE(TransformationUtils::is_active_status(std::any(std::string("no"))));          // No
    EXPECT_FALSE(TransformationUtils::is_active_status(std::any(0)));                          // Zero int
    EXPECT_FALSE(TransformationUtils::is_active_status(std::any(false)));                      // False bool
}

// Test inactive status identification
TEST_F(TransformationUtilsTest, InactiveStatusIdentification) {
    // Valid inactive status values
    EXPECT_TRUE(TransformationUtils::is_inactive_status(std::any(std::string("inactive"))));
    EXPECT_TRUE(TransformationUtils::is_inactive_status(std::any(std::string("I"))));
    EXPECT_TRUE(TransformationUtils::is_inactive_status(std::any(std::string("0"))));
    EXPECT_TRUE(TransformationUtils::is_inactive_status(std::any(std::string("false"))));
    EXPECT_TRUE(TransformationUtils::is_inactive_status(std::any(std::string("no"))));
    EXPECT_TRUE(TransformationUtils::is_inactive_status(std::any(std::string("disabled"))));
    EXPECT_TRUE(TransformationUtils::is_inactive_status(std::any(0)));
    EXPECT_TRUE(TransformationUtils::is_inactive_status(std::any(false)));
    
    // Invalid inactive status values
    EXPECT_FALSE(TransformationUtils::is_inactive_status(std::any()));                         // No value
    EXPECT_FALSE(TransformationUtils::is_inactive_status(std::any(std::string("active"))));    // Active
    EXPECT_FALSE(TransformationUtils::is_inactive_status(std::any(std::string("1"))));         // One
    EXPECT_FALSE(TransformationUtils::is_inactive_status(std::any(std::string("true"))));      // True
    EXPECT_FALSE(TransformationUtils::is_inactive_status(std::any(std::string("yes"))));       // Yes
    EXPECT_FALSE(TransformationUtils::is_inactive_status(std::any(1)));                        // One int
    EXPECT_FALSE(TransformationUtils::is_inactive_status(std::any(true)));                     // True bool
}

// Test enabled status identification
TEST_F(TransformationUtilsTest, EnabledStatusIdentification) {
    // Valid enabled status values
    EXPECT_TRUE(TransformationUtils::is_enabled_status(std::any(std::string("enabled"))));
    EXPECT_TRUE(TransformationUtils::is_enabled_status(std::any(std::string("E"))));
    EXPECT_TRUE(TransformationUtils::is_enabled_status(std::any(std::string("1"))));
    EXPECT_TRUE(TransformationUtils::is_enabled_status(std::any(std::string("true"))));
    EXPECT_TRUE(TransformationUtils::is_enabled_status(std::any(std::string("yes"))));
    EXPECT_TRUE(TransformationUtils::is_enabled_status(std::any(std::string("active"))));
    EXPECT_TRUE(TransformationUtils::is_enabled_status(std::any(1)));
    EXPECT_TRUE(TransformationUtils::is_enabled_status(std::any(true)));
    
    // Invalid enabled status values
    EXPECT_FALSE(TransformationUtils::is_enabled_status(std::any()));                          // No value
    EXPECT_FALSE(TransformationUtils::is_enabled_status(std::any(std::string("disabled"))));   // Disabled
    EXPECT_FALSE(TransformationUtils::is_enabled_status(std::any(std::string("0"))));          // Zero
    EXPECT_FALSE(TransformationUtils::is_enabled_status(std::any(std::string("false"))));      // False
    EXPECT_FALSE(TransformationUtils::is_enabled_status(std::any(std::string("no"))));         // No
    EXPECT_FALSE(TransformationUtils::is_enabled_status(std::any(0)));                         // Zero int
    EXPECT_FALSE(TransformationUtils::is_enabled_status(std::any(false)));                     // False bool
}

// Test disabled status identification
TEST_F(TransformationUtilsTest, DisabledStatusIdentification) {
    // Valid disabled status values
    EXPECT_TRUE(TransformationUtils::is_disabled_status(std::any(std::string("disabled"))));
    EXPECT_TRUE(TransformationUtils::is_disabled_status(std::any(std::string("D"))));
    EXPECT_TRUE(TransformationUtils::is_disabled_status(std::any(std::string("0"))));
    EXPECT_TRUE(TransformationUtils::is_disabled_status(std::any(std::string("false"))));
    EXPECT_TRUE(TransformationUtils::is_disabled_status(std::any(std::string("no"))));
    EXPECT_TRUE(TransformationUtils::is_disabled_status(std::any(std::string("inactive"))));
    EXPECT_TRUE(TransformationUtils::is_disabled_status(std::any(0)));
    EXPECT_TRUE(TransformationUtils::is_disabled_status(std::any(false)));
    
    // Invalid disabled status values
    EXPECT_FALSE(TransformationUtils::is_disabled_status(std::any()));                         // No value
    EXPECT_FALSE(TransformationUtils::is_disabled_status(std::any(std::string("enabled"))));   // Enabled
    EXPECT_FALSE(TransformationUtils::is_disabled_status(std::any(std::string("1"))));         // One
    EXPECT_FALSE(TransformationUtils::is_disabled_status(std::any(std::string("true"))));      // True
    EXPECT_FALSE(TransformationUtils::is_disabled_status(std::any(std::string("yes"))));       // Yes
    EXPECT_FALSE(TransformationUtils::is_disabled_status(std::any(1)));                        // One int
    EXPECT_FALSE(TransformationUtils::is_disabled_status(std::any(true)));                     // True bool
}

// Test visible status identification
TEST_F(TransformationUtilsTest, VisibleStatusIdentification) {
    // Valid visible status values
    EXPECT_TRUE(TransformationUtils::is_visible_status(std::any(std::string("visible"))));
    EXPECT_TRUE(TransformationUtils::is_visible_status(std::any(std::string("V"))));
    EXPECT_TRUE(TransformationUtils::is_visible_status(std::any(std::string("1"))));
    EXPECT_TRUE(TransformationUtils::is_visible_status(std::any(std::string("true"))));
    EXPECT_TRUE(TransformationUtils::is_visible_status(std::any(std::string("yes"))));
    EXPECT_TRUE(TransformationUtils::is_visible_status(std::any(std::string("show"))));
    EXPECT_TRUE(TransformationUtils::is_visible_status(std::any(1)));
    EXPECT_TRUE(TransformationUtils::is_visible_status(std::any(true)));
    
    // Invalid visible status values
    EXPECT_FALSE(TransformationUtils::is_visible_status(std::any()));                          // No value
    EXPECT_FALSE(TransformationUtils::is_visible_status(std::any(std::string("hidden"))));     // Hidden
    EXPECT_FALSE(TransformationUtils::is_visible_status(std::any(std::string("0"))));          // Zero
    EXPECT_FALSE(TransformationUtils::is_visible_status(std::any(std::string("false"))));      // False
    EXPECT_FALSE(TransformationUtils::is_visible_status(std::any(std::string("no"))));         // No
    EXPECT_FALSE(TransformationUtils::is_visible_status(std::any(std::string("hide"))));       // Hide
    EXPECT_FALSE(TransformationUtils::is_visible_status(std::any(0)));                         // Zero int
    EXPECT_FALSE(TransformationUtils::is_visible_status(std::any(false)));                     // False bool
}

// Test hidden status identification
TEST_F(TransformationUtilsTest, HiddenStatusIdentification) {
    // Valid hidden status values
    EXPECT_TRUE(TransformationUtils::is_hidden_status(std::any(std::string("hidden"))));
    EXPECT_TRUE(TransformationUtils::is_hidden_status(std::any(std::string("H"))));
    EXPECT_TRUE(TransformationUtils::is_hidden_status(std::any(std::string("0"))));
    EXPECT_TRUE(TransformationUtils::is_hidden_status(std::any(std::string("false"))));
    EXPECT_TRUE(TransformationUtils::is_hidden_status(std::any(std::string("no"))));
    EXPECT_TRUE(TransformationUtils::is_hidden_status(std::any(std::string("hide"))));
    EXPECT_TRUE(TransformationUtils::is_hidden_status(std::any(0)));
    EXPECT_TRUE(TransformationUtils::is_hidden_status(std::any(false)));
    
    // Invalid hidden status values
    EXPECT_FALSE(TransformationUtils::is_hidden_status(std::any()));                           // No value
    EXPECT_FALSE(TransformationUtils::is_hidden_status(std::any(std::string("visible"))));     // Visible
    EXPECT_FALSE(TransformationUtils::is_hidden_status(std::any(std::string("1"))));           // One
    EXPECT_FALSE(TransformationUtils::is_hidden_status(std::any(std::string("true"))));        // True
    EXPECT_FALSE(TransformationUtils::is_hidden_status(std::any(std::string("yes"))));         // Yes
    EXPECT_FALSE(TransformationUtils::is_hidden_status(std::any(std::string("show"))));        // Show
    EXPECT_FALSE(TransformationUtils::is_hidden_status(std::any(1)));                          // One int
    EXPECT_FALSE(TransformationUtils::is_hidden_status(std::any(true)));                       // True bool
}

// Test editable status identification
TEST_F(TransformationUtilsTest, EditableStatusIdentification) {
    // Valid editable status values
    EXPECT_TRUE(TransformationUtils::is_editable_status(std::any(std::string("editable"))));
    EXPECT_TRUE(TransformationUtils::is_editable_status(std::any(std::string("edit"))));
    EXPECT_TRUE(TransformationUtils::is_editable_status(std::any(std::string("1"))));
    EXPECT_TRUE(TransformationUtils::is_editable_status(std::any(std::string("true"))));
    EXPECT_TRUE(TransformationUtils::is_editable_status(std::any(std::string("yes"))));
    EXPECT_TRUE(TransformationUtils::is_editable_status(std::any(std::string("modifiable"))));
    EXPECT_TRUE(TransformationUtils::is_editable_status(std::any(1)));
    EXPECT_TRUE(TransformationUtils::is_editable_status(std::any(true)));
    
    // Invalid editable status values
    EXPECT_FALSE(TransformationUtils::is_editable_status(std::any()));                         // No value
    EXPECT_FALSE(TransformationUtils::is_editable_status(std::any(std::string("readonly"))));  // Readonly
    EXPECT_FALSE(TransformationUtils::is_editable_status(std::any(std::string("0"))));         // Zero
    EXPECT_FALSE(TransformationUtils::is_editable_status(std::any(std::string("false"))));     // False
    EXPECT_FALSE(TransformationUtils::is_editable_status(std::any(std::string("no"))));        // No
    EXPECT_FALSE(TransformationUtils::is_editable_status(std::any(0)));                        // Zero int
    EXPECT_FALSE(TransformationUtils::is_editable_status(std::any(false)));                    // False bool
}

// Test readonly status identification
TEST_F(TransformationUtilsTest, ReadonlyStatusIdentification) {
    // Valid readonly status values
    EXPECT_TRUE(TransformationUtils::is_readonly_status(std::any(std::string("readonly"))));
    EXPECT_TRUE(TransformationUtils::is_readonly_status(std::any(std::string("ro"))));
    EXPECT_TRUE(TransformationUtils::is_readonly_status(std::any(std::string("0"))));
    EXPECT_TRUE(TransformationUtils::is_readonly_status(std::any(std::string("false"))));
    EXPECT_TRUE(TransformationUtils::is_readonly_status(std::any(std::string("no"))));
    EXPECT_TRUE(TransformationUtils::is_readonly_status(std::any(std::string("read-only"))));
    EXPECT_TRUE(TransformationUtils::is_readonly_status(std::any(0)));
    EXPECT_TRUE(TransformationUtils::is_readonly_status(std::any(false)));
    
    // Invalid readonly status values
    EXPECT_FALSE(TransformationUtils::is_readonly_status(std::any()));                         // No value
    EXPECT_FALSE(TransformationUtils::is_readonly_status(std::any(std::string("editable"))));  // Editable
    EXPECT_FALSE(TransformationUtils::is_readonly_status(std::any(std::string("1"))));         // One
    EXPECT_FALSE(TransformationUtils::is_readonly_status(std::any(std::string("true"))));      // True
    EXPECT_FALSE(TransformationUtils::is_readonly_status(std::any(std::string("yes"))));       // Yes
    EXPECT_FALSE(TransformationUtils::is_readonly_status(std::any(1)));                        // One int
    EXPECT_FALSE(TransformationUtils::is_readonly_status(std::any(true)));                     // True bool
}

} // namespace omop::transform::test