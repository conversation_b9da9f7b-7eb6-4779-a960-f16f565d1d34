#include "transform/date_transformations.h"
#include "common/logging.h"
#include <iomanip>
#include <sstream>
#include <mutex>

namespace omop::transform {

/**
 * @brief Date calculation transformation
 *
 * Performs calculations on dates such as age calculation, date differences,
 * and date arithmetic operations.
 */
TransformationResult DateCalculationTransformation::transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) {
    TransformationResult result;

    try {
        if (!validate_input(input)) {
            result.set_error("Invalid input for date calculation");
            return result;
        }

        std::chrono::system_clock::time_point date;

        // Parse input date with UK-compatible formats
        if (input.type() == typeid(std::string)) {
            // Try primary format first, then UK alternatives
            std::vector<std::string> formats = {input_format_};
            
            // Add UK alternative formats if not already included
            if (input_format_ == "%d/%m/%Y") {
                formats.push_back("%d.%m.%Y");  // Dot separator
                formats.push_back("%d-%m-%Y");  // Dash separator
            }
            
            auto parsed = TransformationUtils::parse_date(
                std::any_cast<std::string>(input),
                formats);
            if (!parsed) {
                result.set_error("Failed to parse input date");
                return result;
            }
            date = *parsed;
        } else if (input.type() == typeid(std::chrono::system_clock::time_point)) {
            date = std::any_cast<std::chrono::system_clock::time_point>(input);
        } else {
            result.set_error("Unsupported input type for date calculation");
            return result;
        }

        // Perform calculation
        switch (operation_) {
            case Operation::Age: {
                if (!reference_date_) {
                    reference_date_ = std::chrono::system_clock::now();
                }
                int age = TransformationUtils::calculate_age(date, *reference_date_);
                result.value = age;
                break;
            }

            case Operation::DateDiff: {
                if (!reference_date_) {
                    result.set_error("Reference date required for date difference calculation");
                    return result;
                }
                int diff = TransformationUtils::calculate_date_difference(
                    date, *reference_date_, diff_unit_);
                result.value = diff;
                break;
            }

            case Operation::AddDays: {
                auto time_value = std::chrono::system_clock::to_time_t(date);
                std::tm tm;
                
                // Use thread-safe localtime
#ifdef _WIN32
                localtime_s(&tm, &time_value);
#else
                localtime_r(&time_value, &tm);
#endif
                tm.tm_mday += static_cast<int>(offset_value_);

                // Handle month/year rollover
                while (tm.tm_mday > 28) {
                    int days_in_month = 31;
                    if (tm.tm_mon == 1) { // February
                        days_in_month = (tm.tm_year % 4 == 0 && (tm.tm_year % 100 != 0 || tm.tm_year % 400 == 0)) ? 29 : 28;
                    } else if (tm.tm_mon == 3 || tm.tm_mon == 5 || tm.tm_mon == 8 || tm.tm_mon == 10) { // April, June, September, November
                        days_in_month = 30;
                    }
                    
                    if (tm.tm_mday > days_in_month) {
                        tm.tm_mday -= days_in_month;
                        tm.tm_mon++;
                        if (tm.tm_mon >= 12) {
                            tm.tm_mon = 0;
                            tm.tm_year++;
                        }
                    } else {
                        break;
                    }
                }

                time_t new_time_t;
                {
                    static std::mutex time_mutex;
                    std::lock_guard<std::mutex> lock(time_mutex);
                    new_time_t = std::mktime(&tm);
                }
                auto new_date = std::chrono::system_clock::from_time_t(new_time_t);
                result.value = format_output(new_date);
                break;
            }

            case Operation::AddMonths: {
                auto time_value = std::chrono::system_clock::to_time_t(date);
                std::tm tm;
                
                // Use thread-safe localtime
#ifdef _WIN32
                localtime_s(&tm, &time_value);
#else
                localtime_r(&time_value, &tm);
#endif
                tm.tm_mon += static_cast<int>(offset_value_);

                // Handle year rollover
                while (tm.tm_mon >= 12) {
                    tm.tm_mon -= 12;
                    tm.tm_year++;
                }

                time_t new_time_t;
                {
                    static std::mutex time_mutex;
                    std::lock_guard<std::mutex> lock(time_mutex);
                    new_time_t = std::mktime(&tm);
                }
                auto new_date = std::chrono::system_clock::from_time_t(new_time_t);
                result.value = format_output(new_date);
                break;
            }

            case Operation::AddYears: {
                auto time_value = std::chrono::system_clock::to_time_t(date);
                std::tm tm;
                
                // Use thread-safe localtime
#ifdef _WIN32
                localtime_s(&tm, &time_value);
#else
                localtime_r(&time_value, &tm);
#endif
                tm.tm_year += static_cast<int>(offset_value_);

                time_t new_time_t;
                {
                    static std::mutex time_mutex;
                    std::lock_guard<std::mutex> lock(time_mutex);
                    new_time_t = std::mktime(&tm);
                }
                auto new_date = std::chrono::system_clock::from_time_t(new_time_t);
                result.value = format_output(new_date);
                break;
            }

            case Operation::StartOfMonth: {
                auto time_value = std::chrono::system_clock::to_time_t(date);
                std::tm tm;
                
                // Use thread-safe localtime
#ifdef _WIN32
                localtime_s(&tm, &time_value);
#else
                localtime_r(&time_value, &tm);
#endif
                tm.tm_mday = 1;
                tm.tm_hour = 0;
                tm.tm_min = 0;
                tm.tm_sec = 0;

                time_t new_time_t;
                {
                    static std::mutex time_mutex;
                    std::lock_guard<std::mutex> lock(time_mutex);
                    new_time_t = std::mktime(&tm);
                }
                auto new_date = std::chrono::system_clock::from_time_t(new_time_t);
                result.value = format_output(new_date);
                break;
            }

            case Operation::EndOfMonth: {
                auto time_value = std::chrono::system_clock::to_time_t(date);
                std::tm tm;
                
                // Use thread-safe localtime
#ifdef _WIN32
                localtime_s(&tm, &time_value);
#else
                localtime_r(&time_value, &tm);
#endif
                tm.tm_mon++;
                tm.tm_mday = 0; // Last day of previous month
                tm.tm_hour = 23;
                tm.tm_min = 59;
                tm.tm_sec = 59;

                time_t new_time_t;
                {
                    static std::mutex time_mutex;
                    std::lock_guard<std::mutex> lock(time_mutex);
                    new_time_t = std::mktime(&tm);
                }
                auto new_date = std::chrono::system_clock::from_time_t(new_time_t);
                result.value = format_output(new_date);
                break;
            }

            case Operation::StartOfYear: {
                auto time_value = std::chrono::system_clock::to_time_t(date);
                std::tm tm;
                
                // Use thread-safe gmtime
#ifdef _WIN32
                gmtime_s(&tm, &time_value);
#else
                gmtime_r(&time_value, &tm);
#endif
                tm.tm_mon = 0;
                tm.tm_mday = 1;
                tm.tm_hour = 0;
                tm.tm_min = 0;
                tm.tm_sec = 0;

                // Portable UTC time conversion
                time_t new_time_t;
#ifdef _WIN32
                new_time_t = _mkgmtime(&tm);
#else
                // Use a portable implementation for timegm if available
                tm.tm_isdst = 0;
                new_time_t = std::mktime(&tm);
                if (new_time_t != static_cast<time_t>(-1)) {
                    // Adjust for timezone offset
                    std::tm utc_tm;
                    gmtime_r(&new_time_t, &utc_tm);
                    time_t utc_time = std::mktime(&utc_tm);
                    new_time_t = new_time_t + (new_time_t - utc_time);
                }
#endif
                auto new_date = std::chrono::system_clock::from_time_t(new_time_t);
                result.value = format_output(new_date);
                break;
            }

            case Operation::EndOfYear: {
                auto time_value = std::chrono::system_clock::to_time_t(date);
                std::tm tm;
                
                // Use thread-safe gmtime
#ifdef _WIN32
                gmtime_s(&tm, &time_value);
#else
                gmtime_r(&time_value, &tm);
#endif
                tm.tm_mon = 11;
                tm.tm_mday = 31;
                tm.tm_hour = 23;
                tm.tm_min = 59;
                tm.tm_sec = 59;

                // Portable UTC time conversion
                time_t new_time_t;
#ifdef _WIN32
                new_time_t = _mkgmtime(&tm);
#else
                // Use a portable implementation for timegm if available
                tm.tm_isdst = 0;
                new_time_t = std::mktime(&tm);
                if (new_time_t != static_cast<time_t>(-1)) {
                    // Adjust for timezone offset
                    std::tm utc_tm;
                    gmtime_r(&new_time_t, &utc_tm);
                    time_t utc_time = std::mktime(&utc_tm);
                    new_time_t = new_time_t + (new_time_t - utc_time);
                }
#endif
                auto new_date = std::chrono::system_clock::from_time_t(new_time_t);
                result.value = format_output(new_date);
                break;
            }
        }

        // Add metadata
        result.metadata["operation"] = get_operation_name();
        result.metadata["input_date"] = TransformationUtils::format_date(
            date, constants::DEFAULT_DATETIME_FORMAT);

    } catch (const std::exception& e) {
        result.set_error(std::format("Date calculation failed: {}", e.what()));
        context.increment_errors();
    }

    return result;
}

bool DateCalculationTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    return input.type() == typeid(std::string) ||
           input.type() == typeid(std::chrono::system_clock::time_point);
}

void DateCalculationTransformation::configure(const YAML::Node& params) {
    if (params["operation"]) {
        std::string op_str = params["operation"].as<std::string>();
        configure_operation(op_str);
    }

    if (params["input_format"]) {
        input_format_ = params["input_format"].as<std::string>();
    }

    if (params["output_format"]) {
        output_format_ = params["output_format"].as<std::string>();
    }

    if (params["reference_date"]) {
        std::string ref_date_str = params["reference_date"].as<std::string>();
        if (ref_date_str == "now" || ref_date_str == "today") {
            reference_date_ = std::chrono::system_clock::now();
        } else {
            auto parsed = TransformationUtils::parse_date(
                ref_date_str, {input_format_});
            if (parsed) {
                reference_date_ = *parsed;
            }
        }
    }

    if (params["offset"]) {
        offset_value_ = params["offset"].as<double>();
    }

    if (params["unit"]) {
        diff_unit_ = params["unit"].as<std::string>();
    }
}

void DateCalculationTransformation::configure_operation(const std::string& op_str) {
    static const std::unordered_map<std::string, Operation> op_map = {
        {"age", Operation::Age},
        {"calculate_age", Operation::Age},
        {"date_diff", Operation::DateDiff},
        {"date_difference", Operation::DateDiff},
        {"add_days", Operation::AddDays},
        {"add_months", Operation::AddMonths},
        {"add_years", Operation::AddYears},
        {"start_of_month", Operation::StartOfMonth},
        {"end_of_month", Operation::EndOfMonth},
        {"start_of_year", Operation::StartOfYear},
        {"end_of_year", Operation::EndOfYear}
    };

    auto it = op_map.find(op_str);
    if (it != op_map.end()) {
        operation_ = it->second;
    }
}

std::string DateCalculationTransformation::get_operation_name() const {
    switch (operation_) {
        case Operation::Age: return "age";
        case Operation::DateDiff: return "date_diff";
        case Operation::AddDays: return "add_days";
        case Operation::AddMonths: return "add_months";
        case Operation::AddYears: return "add_years";
        case Operation::StartOfMonth: return "start_of_month";
        case Operation::EndOfMonth: return "end_of_month";
        case Operation::StartOfYear: return "start_of_year";
        case Operation::EndOfYear: return "end_of_year";
        default: return "unknown";
    }
}

std::any DateCalculationTransformation::format_output(const std::chrono::system_clock::time_point& date) {
    return TransformationUtils::format_date(date, output_format_);
}

/**
 * @brief Date range validation transformation
 *
 * Validates that dates fall within acceptable ranges for OMOP CDM.
 */
TransformationResult DateRangeValidationTransformation::transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) {
    TransformationResult result;

    try {
        if (!validate_input(input)) {
            result.set_error("Invalid input for date range validation");
            return result;
        }

        std::chrono::system_clock::time_point date;

        // Parse input date with UK-compatible formats
        if (input.type() == typeid(std::string)) {
            // Try primary format first, then UK alternatives
            std::vector<std::string> formats = {input_format_};
            
            // Add UK alternative formats if not already included
            if (input_format_ == "%d/%m/%Y") {
                formats.push_back("%d.%m.%Y");  // Dot separator
                formats.push_back("%d-%m-%Y");  // Dash separator
            }
            
            auto parsed = TransformationUtils::parse_date(
                std::any_cast<std::string>(input),
                formats);
            if (!parsed) {
                result.set_error("Failed to parse input date");
                return result;
            }
            date = *parsed;
        } else if (input.type() == typeid(std::chrono::system_clock::time_point)) {
            date = std::any_cast<std::chrono::system_clock::time_point>(input);
        } else {
            result.set_error("Unsupported input type for date validation");
            return result;
        }

        // Check for future dates
        if (reject_future_dates_) {
            auto now = std::chrono::system_clock::now();
            if (date > now) {
                if (set_future_to_today_) {
                    date = now;
                    result.add_warning("Future date set to today");
                } else {
                    result.set_error("Future dates are not allowed");
                    return result;
                }
            }
        }

        // Check range constraints
        if (min_date_ && date < *min_date_) {
            if (clamp_to_range_) {
                date = *min_date_;
                result.add_warning("Date clamped to minimum");
            } else {
                result.set_error("Date is before minimum allowed date");
                return result;
            }
        }

        if (max_date_ && date > *max_date_) {
            if (clamp_to_range_) {
                date = *max_date_;
                result.add_warning("Date clamped to maximum");
            } else {
                result.set_error("Date is after maximum allowed date");
                return result;
            }
        }

        result.value = TransformationUtils::format_date(date, output_format_);
        // Store original input in metadata
        if (input.type() == typeid(std::chrono::system_clock::time_point)) {
            result.metadata["original_date"] = TransformationUtils::format_date(
                std::any_cast<std::chrono::system_clock::time_point>(input),
                constants::DEFAULT_DATETIME_FORMAT);
        } else if (input.type() == typeid(std::string)) {
            result.metadata["original_date"] = std::any_cast<std::string>(input);
        }

    } catch (const std::exception& e) {
        result.set_error(std::format("Date validation failed: {}", e.what()));
        context.increment_errors();
    }

    return result;
}

bool DateRangeValidationTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    return input.type() == typeid(std::string) ||
           input.type() == typeid(std::chrono::system_clock::time_point);
}

void DateRangeValidationTransformation::configure(const YAML::Node& params) {
    if (params["input_format"]) {
        input_format_ = params["input_format"].as<std::string>();
    }
    if (params["output_format"]) {
        output_format_ = params["output_format"].as<std::string>();
    }

    if (params["min_date"]) {
        auto parsed = TransformationUtils::parse_date(
            params["min_date"].as<std::string>(),
            {input_format_});
        if (parsed) {
            min_date_ = *parsed;
        }
    }

    if (params["max_date"]) {
        auto parsed = TransformationUtils::parse_date(
            params["max_date"].as<std::string>(),
            {input_format_});
        if (parsed) {
            max_date_ = *parsed;
        }
    }

    if (params["clamp_to_range"]) {
        clamp_to_range_ = params["clamp_to_range"].as<bool>();
    }

    if (params["reject_future_dates"]) {
        reject_future_dates_ = params["reject_future_dates"].as<bool>();
    }

    if (params["set_future_to_today"]) {
        set_future_to_today_ = params["set_future_to_today"].as<bool>();
    }
}

// Register date transformations
static bool register_date_transformations() {
    auto& registry = TransformationRegistry::instance();

    registry.register_transformation("date_calculation",
        []() { return std::make_unique<DateCalculationTransformation>(); });

    registry.register_transformation("date_range_validation",
        []() { return std::make_unique<DateRangeValidationTransformation>(); });

    return true;
}

static bool date_transformations_registered = register_date_transformations();

} // namespace omop::transform