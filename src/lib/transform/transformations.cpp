#include "transform/transformations.h"
#include "common/logging.h"
#include <stdexcept>

namespace omop::transform {

void TransformationRegistry::register_transformation(
    const std::string& type_name,
    std::function<std::unique_ptr<FieldTransformation>()> factory) {
    
    std::lock_guard<std::mutex> lock(registry_mutex_);
    
    if (type_name.empty()) {
        throw std::invalid_argument("Transformation type name cannot be empty");
    }
    
    factories_[type_name] = std::move(factory);
    
    // Log the registration
    auto logger = spdlog::get("omop-transform");
    if (logger) {
        logger->debug("Registered transformation type: {}", type_name);
    }
}

std::unique_ptr<FieldTransformation> TransformationRegistry::create_transformation(
    const std::string& type_name) {
    
    std::lock_guard<std::mutex> lock(registry_mutex_);
    
    auto it = factories_.find(type_name);
    if (it == factories_.end()) {
        auto logger = spdlog::get("omop-transform");
        if (logger) {
            logger->error("Unknown transformation type: {}", type_name);
        }
        return nullptr;
    }
    
    try {
        return it->second();
    } catch (const std::exception& e) {
        auto logger = spdlog::get("omop-transform");
        if (logger) {
            logger->error("Failed to create transformation '{}': {}", type_name, e.what());
        }
        return nullptr;
    }
}

bool TransformationRegistry::has_transformation(const std::string& type_name) const {
    std::lock_guard<std::mutex> lock(registry_mutex_);
    return factories_.find(type_name) != factories_.end();
}

std::vector<std::string> TransformationRegistry::get_registered_types() const {
    std::lock_guard<std::mutex> lock(registry_mutex_);
    
    std::vector<std::string> types;
    types.reserve(factories_.size());
    
    for (const auto& [type_name, factory] : factories_) {
        types.push_back(type_name);
    }
    
    std::sort(types.begin(), types.end());
    return types;
}

} // namespace omop::transform